import { createClient } from "@supabase/supabase-js";
import { NextResponse } from "next/server";
import { parse } from "papaparse";
import * as XLSX from "xlsx";

// Initialize Supabase client with SERVICE_ROLE key for admin actions
let supabaseAdmin: ReturnType<typeof createClient> | null = null;
try {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl) {
    console.error(
      "[API Init] Missing environment variable: NEXT_PUBLIC_SUPABASE_URL",
    );
    throw new Error("Server configuration error: Supabase URL missing.");
  }
  if (!serviceKey) {
    console.error(
      "[API Init] Missing environment variable: SUPABASE_SERVICE_ROLE_KEY",
    );
    throw new Error(
      "Server configuration error: Supabase service role key missing.",
    );
  }

  supabaseAdmin = createClient(supabaseUrl, serviceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
  console.log("[API Init] Supabase admin client initialized successfully.");
} catch (initError) {
  console.error(
    "[API Init] Failed to initialize Supabase admin client:",
    initError,
  );
  // supabaseAdmin remains null if initialization fails
}

// Helper function to validate email
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Helper function to generate a random password
// This function is used in the confirm-import-users route
export function generateRandomPassword(length = 10): string {
  const charset =
    "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+";
  let password = "";
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }
  return password;
}

interface UserData {
  email: string;
  firstName: string;
  lastName: string;
  identityDocument: string;
}

// Helper function to validate user data
function validateUserData(userData: Partial<UserData>): string[] {
  const errors: string[] = [];

  // Check required fields
  if (!userData.email) errors.push("Email es requerido");
  if (!userData.firstName) errors.push("Nombre es requerido");
  if (!userData.lastName) errors.push("Apellido es requerido");
  if (!userData.identityDocument)
    errors.push("Documento de identidad es requerido");

  // Validate email format
  if (userData.email && !isValidEmail(userData.email)) {
    errors.push("Formato de email inválido");
  }

  return errors;
}

// Helper function to normalize column names
function normalizeColumnName(name: string): string {
  const lowerName = name.toLowerCase().trim();

  // Map common column names to our expected format
  const columnMap: Record<string, string> = {
    // Email variations
    email: "email",
    correo: "email",
    "correo electrónico": "email",
    "correo electronico": "email",
    "e-mail": "email",

    // First name variations
    "first name": "firstName",
    firstname: "firstName",
    nombre: "firstName",
    nombres: "firstName",
    first_name: "firstName",

    // Last name variations
    "last name": "lastName",
    lastname: "lastName",
    apellido: "lastName",
    apellidos: "lastName",
    last_name: "lastName",

    // Identity document variations
    "identity document": "identityDocument",
    identitydocument: "identityDocument",
    documento: "identityDocument",
    "documento de identidad": "identityDocument",
    rut: "identityDocument",
    identity_document: "identityDocument",
    documento_identidad: "identityDocument",
    dni: "identityDocument",
  };

  return columnMap[lowerName] || lowerName;
}

// Process CSV data
async function processCSVData(csvData: string): Promise<Partial<UserData>[]> {
  return new Promise((resolve, reject) => {
    parse(csvData, {
      header: true,
      skipEmptyLines: true,
      transformHeader: (header) => normalizeColumnName(header),
      complete: (results) => {
        resolve(results.data as Partial<UserData>[]);
      },
      error: (error) => {
        reject(error);
      },
    });
  });
}

// Process Excel data
function processExcelData(buffer: ArrayBuffer): Partial<UserData>[] {
  const workbook = XLSX.read(buffer, { type: "array" });
  const firstSheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[firstSheetName];

  // Convert to JSON with headers
  const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

  if (rawData.length < 2) {
    throw new Error("El archivo Excel no contiene datos suficientes");
  }

  // Get headers and normalize them
  const headers = (rawData[0] as string[]).map((header) =>
    normalizeColumnName(header),
  );

  // Process rows
  const data: Partial<UserData>[] = [];
  for (let i = 1; i < rawData.length; i++) {
    const row = rawData[i] as unknown[];
    if (row.length === 0) continue; // Skip empty rows

    const rowData: Record<string, string> = {};
    for (let j = 0; j < headers.length; j++) {
      rowData[headers[j]] = row[j]?.toString() || "";
    }
    data.push(rowData as Partial<UserData>);
  }

  return data;
}

// POST handler to process file upload
export async function POST(request: Request) {
  try {
    if (!supabaseAdmin) {
      console.error(
        "[API POST] Supabase admin client is not available due to initialization error.",
      );
      return NextResponse.json(
        { error: "Server configuration error." },
        { status: 500 },
      );
    }

    // Get form data from request
    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Check file type
    const fileType = file.name.split(".").pop()?.toLowerCase();

    // Process file based on type
    let userData: Partial<UserData>[] = [];

    if (fileType === "csv") {
      // Process CSV file
      const csvText = await file.text();
      userData = await processCSVData(csvText);
    } else if (fileType === "xlsx" || fileType === "xls") {
      // Process Excel file
      const buffer = await file.arrayBuffer();
      userData = processExcelData(buffer);
    } else {
      return NextResponse.json(
        { error: "Unsupported file type. Please upload a CSV or Excel file." },
        { status: 400 },
      );
    }

    if (userData.length === 0) {
      return NextResponse.json(
        { error: "No data found in the file" },
        { status: 400 },
      );
    }

    // Check if we have the required columns
    const requiredColumns = [
      "email",
      "firstName",
      "lastName",
      "identityDocument",
    ];
    const missingColumns = requiredColumns.filter(
      (col) =>
        !Object.keys(userData[0]).some(
          (key) => normalizeColumnName(key) === col,
        ),
    );

    if (missingColumns.length > 0) {
      return NextResponse.json(
        {
          error: `Missing required columns: ${missingColumns.join(", ")}. Please make sure your file has columns for email, first name, last name, and identity document.`,
        },
        { status: 400 },
      );
    }

    // Validate and process each user
    const processedUsers = [];
    const existingEmails = new Set<string>();
    const existingDocuments = new Set<string>();

    // First, get existing users to check for duplicates
    const { data: existingUsers, error: fetchError } = await supabaseAdmin
      .from("users")
      .select("email, identity_document");

    if (fetchError) {
      console.error("[API POST] Error fetching existing users:", fetchError);
      return NextResponse.json({ error: fetchError.message }, { status: 500 });
    }

    // Add existing users to sets for quick lookup
    existingUsers?.forEach((user) => {
      if (user.email) existingEmails.add(user.email.toLowerCase());
      if (user.identity_document)
        existingDocuments.add(user.identity_document.toLowerCase());
    });

    // Process each user
    for (const user of userData) {
      const processedUser = {
        email: user.email?.trim() || "",
        firstName: user.firstName?.trim() || "",
        lastName: user.lastName?.trim() || "",
        identityDocument: user.identityDocument?.trim() || "",
        status: "valid" as "valid" | "invalid" | "duplicate",
        errors: [] as string[],
      };

      // Validate user data
      const validationErrors = validateUserData(processedUser);

      if (validationErrors.length > 0) {
        processedUser.status = "invalid";
        processedUser.errors = validationErrors;
      } else if (
        existingEmails.has(processedUser.email.toLowerCase()) ||
        existingDocuments.has(processedUser.identityDocument.toLowerCase())
      ) {
        processedUser.status = "duplicate";
        processedUser.errors = ["Usuario ya existe en el sistema"];
      }

      processedUsers.push(processedUser);
    }

    // Return processed users for preview
    return NextResponse.json({
      success: true,
      users: processedUsers,
      total: processedUsers.length,
      valid: processedUsers.filter((u) => u.status === "valid").length,
      invalid: processedUsers.filter((u) => u.status === "invalid").length,
      duplicate: processedUsers.filter((u) => u.status === "duplicate").length,
    });
  } catch (error) {
    console.error("[API POST] Error processing file:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred";
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
