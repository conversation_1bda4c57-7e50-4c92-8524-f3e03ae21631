/**
 * User Repository
 *
 * This file implements the repository pattern for User entities.
 * It provides domain-specific methods for user management operations
 * while abstracting away the underlying database implementation.
 */

import type { QueryFilter, User } from "../adapters/database/types";
import { BaseRepository } from "./base-repository";

// ============================================================================
// User Repository Interface
// ============================================================================

export interface UserRepositoryInterface {
  // Basic CRUD operations (inherited from BaseRepository)

  // User-specific operations
  findByEmail(email: string): Promise<User | null>;
  findByIdentityDocument(identityDocument: string): Promise<User | null>;
  findByRole(role: "admin" | "student" | "instructor"): Promise<User[]>;
  findActiveUsers(): Promise<User[]>;
  findByCompany(companyId: string): Promise<User[]>;

  // Authentication-related operations
  getUserInfo(userId?: string): Promise<any>;
  updateLastSignIn(userId: string): Promise<void>;

  // Admin operations
  activateUser(userId: string): Promise<User | null>;
  deactivateUser(userId: string): Promise<User | null>;
  changeUserRole(
    userId: string,
    newRole: "admin" | "student" | "instructor",
  ): Promise<User | null>;

  // Search and filtering
  searchUsers(query: string): Promise<User[]>;
  findUsersWithCertificates(): Promise<User[]>;
}

// ============================================================================
// User Repository Implementation
// ============================================================================

export class UserRepository
  extends BaseRepository<User>
  implements UserRepositoryInterface
{
  protected tableName = "users";

  // ============================================================================
  // User-specific Find Operations
  // ============================================================================

  async findByEmail(email: string): Promise<User | null> {
    return this.findOne([{ column: "email", operator: "eq", value: email }]);
  }

  async findByIdentityDocument(identityDocument: string): Promise<User | null> {
    return this.findOne([
      { column: "identity_document", operator: "eq", value: identityDocument },
    ]);
  }

  async findByRole(role: "admin" | "student" | "instructor"): Promise<User[]> {
    return this.findMany([this.roleFilter(role), this.activeFilter()]);
  }

  async findActiveUsers(): Promise<User[]> {
    return this.findMany([this.activeFilter()]);
  }

  async findByCompany(companyId: string): Promise<User[]> {
    return this.findMany([
      { column: "company_id", operator: "eq", value: companyId },
      this.activeFilter(),
    ]);
  }

  // ============================================================================
  // Authentication-related Operations
  // ============================================================================

  async getUserInfo(userId?: string): Promise<any> {
    try {
      const params = userId ? { user_id: userId } : {};
      return await this.rpc("get_user_info", params);
    } catch (error) {
      console.error("Error getting user info:", error);
      throw error;
    }
  }

  async updateLastSignIn(userId: string): Promise<void> {
    try {
      await this.update(userId, {
        updated_at: new Date().toISOString(),
      } as Partial<User>);
    } catch (error) {
      console.error("Error updating last sign in:", error);
      throw error;
    }
  }

  // ============================================================================
  // Admin Operations
  // ============================================================================

  async activateUser(userId: string): Promise<User | null> {
    return this.update(userId, {
      is_active: true,
      updated_at: new Date().toISOString(),
    } as Partial<User>);
  }

  async deactivateUser(userId: string): Promise<User | null> {
    return this.update(userId, {
      is_active: false,
      updated_at: new Date().toISOString(),
    } as Partial<User>);
  }

  async changeUserRole(
    userId: string,
    newRole: "admin" | "student" | "instructor",
  ): Promise<User | null> {
    return this.update(userId, {
      role: newRole,
      updated_at: new Date().toISOString(),
    } as Partial<User>);
  }

  // ============================================================================
  // Search and Filtering Operations
  // ============================================================================

  async searchUsers(query: string): Promise<User[]> {
    const searchTerm = `%${query}%`;

    return this.findMany([
      {
        column: "first_name",
        operator: "ilike",
        value: searchTerm,
      },
    ]);
  }

  async findUsersWithCertificates(): Promise<User[]> {
    try {
      // This would typically be done with a JOIN, but we'll use RPC for complex queries
      return await this.rpc("get_users_with_certificates");
    } catch (error) {
      console.error("Error finding users with certificates:", error);
      // Fallback to basic user query
      return this.findActiveUsers();
    }
  }

  // ============================================================================
  // Bulk Operations
  // ============================================================================

  async createStudents(studentsData: Partial<User>[]): Promise<User[]> {
    const studentsWithRole = studentsData.map((student) => ({
      ...student,
      role: "student" as const,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }));

    return this.createMany(studentsWithRole);
  }

  async findStudentsByIdentityDocuments(
    identityDocuments: string[],
  ): Promise<User[]> {
    return this.findMany([
      { column: "identity_document", operator: "in", value: identityDocuments },
      this.roleFilter("student"),
      this.activeFilter(),
    ]);
  }

  // ============================================================================
  // Statistics and Analytics
  // ============================================================================

  async getUserStats(): Promise<{
    total: number;
    active: number;
    byRole: Record<string, number>;
  }> {
    try {
      const [total, active, admins, students, instructors] = await Promise.all([
        this.count(),
        this.count([this.activeFilter()]),
        this.count([this.roleFilter("admin"), this.activeFilter()]),
        this.count([this.roleFilter("student"), this.activeFilter()]),
        this.count([this.roleFilter("instructor"), this.activeFilter()]),
      ]);

      return {
        total,
        active,
        byRole: {
          admin: admins,
          student: students,
          instructor: instructors,
        },
      };
    } catch (error) {
      console.error("Error getting user stats:", error);
      throw error;
    }
  }

  // ============================================================================
  // Validation Helpers
  // ============================================================================

  async validateUniqueEmail(
    email: string,
    excludeUserId?: string,
  ): Promise<boolean> {
    const filters: QueryFilter[] = [
      { column: "email", operator: "eq", value: email },
    ];

    if (excludeUserId) {
      filters.push({ column: "id", operator: "neq", value: excludeUserId });
    }

    const existingUser = await this.findOne(filters);
    return existingUser === null;
  }

  async validateUniqueIdentityDocument(
    identityDocument: string,
    excludeUserId?: string,
  ): Promise<boolean> {
    const filters: QueryFilter[] = [
      { column: "identity_document", operator: "eq", value: identityDocument },
    ];

    if (excludeUserId) {
      filters.push({ column: "id", operator: "neq", value: excludeUserId });
    }

    const existingUser = await this.findOne(filters);
    return existingUser === null;
  }
}
