/**
 * @fileoverview Base Repository Class - Foundation for all data access repositories
 *
 * This file provides the base repository implementation that all domain repositories extend.
 * It implements common CRUD operations using the database adapter pattern, providing a
 * consistent interface for data access across all entities in the system.
 *
 * The repository pattern abstracts database operations and allows business logic to work
 * with domain entities without knowing about the underlying database implementation.
 * This enables easy testing, database switching, and maintains clean separation of concerns.
 *
 * Features:
 * - Generic CRUD operations for all entity types
 * - Flexible querying with filters and options
 * - Batch operations for performance optimization
 * - Consistent error handling and response formatting
 * - Database adapter abstraction for provider independence
 * - Built-in validation and data sanitization
 * - Audit trail and logging capabilities
 *
 * @example Basic Repository Usage
 * ```typescript
 * import { BaseRepository } from './base-repository';
 * import { User } from '../adapters/database/types';
 *
 * class UserRepository extends BaseRepository<User> {
 *   protected tableName = 'users';
 *
 *   async findByEmail(email: string): Promise<User | null> {
 *     return this.findOne([
 *       { column: 'email', operator: 'eq', value: email }
 *     ]);
 *   }
 * }
 * ```
 *
 * @example Advanced Querying
 * ```typescript
 * // Find users with complex filters
 * const activeStudents = await userRepository.findMany([
 *   { column: 'role', operator: 'eq', value: 'student' },
 *   { column: 'is_active', operator: 'eq', value: true }
 * ], {
 *   orderBy: 'created_at',
 *   orderDirection: 'desc',
 *   limit: 10
 * });
 * ```
 *
 * <AUTHOR> CURSE Development Team
 * @version 1.0.0
 * @since 2025-01-12
 */

import type {
  DatabaseAdapter,
  QueryFilter,
  QueryOptions,
} from "../adapters/database/types";

// ============================================================================
// Base Repository Interface
// ============================================================================

/**
 * Generic repository interface defining standard CRUD operations
 *
 * This interface establishes the contract that all repositories must implement,
 * ensuring consistent data access patterns across all domain entities.
 *
 * @template T The entity type this repository manages
 *
 * @example Implementation
 * ```typescript
 * class UserRepository extends BaseRepository<User> implements Repository<User> {
 *   protected tableName = 'users';
 *
 *   // All interface methods are implemented by BaseRepository
 *   // Add custom methods as needed
 *   async findByEmail(email: string): Promise<User | null> {
 *     return this.findOne([{ column: 'email', operator: 'eq', value: email }]);
 *   }
 * }
 * ```
 */
export interface Repository<T> {
  /** Retrieve all records with optional query options */
  findAll(options?: QueryOptions): Promise<T[]>;
  /** Find a single record by its ID */
  findById(id: string): Promise<T | null>;
  /** Find the first record matching the given filters */
  findOne(filters: QueryFilter[]): Promise<T | null>;
  /** Find multiple records matching the given filters */
  findMany(filters: QueryFilter[], options?: QueryOptions): Promise<T[]>;
  /** Create a new record */
  create(data: Partial<T>): Promise<T>;
  /** Create multiple records in a single operation */
  createMany(data: Partial<T>[]): Promise<T[]>;
  /** Update a record by its ID */
  update(id: string, data: Partial<T>): Promise<T | null>;
  /** Update multiple records matching the given filters */
  updateMany(filters: QueryFilter[], data: Partial<T>): Promise<T[]>;
  /** Delete a record by its ID */
  delete(id: string): Promise<boolean>;
  /** Delete multiple records matching the given filters */
  deleteMany(filters: QueryFilter[]): Promise<number>;
  /** Count records matching the given filters */
  count(filters?: QueryFilter[]): Promise<number>;
  /** Check if a record exists by its ID */
  exists(id: string): Promise<boolean>;
}

// ============================================================================
// Base Repository Implementation
// ============================================================================

export abstract class BaseRepository<T extends { id: string }>
  implements Repository<T>
{
  protected abstract tableName: string;

  constructor(protected db: DatabaseAdapter) {}

  async findAll(options?: QueryOptions): Promise<T[]> {
    const response = await this.db.select<T>(this.tableName, options);

    if (response.error) {
      throw new Error(
        `Failed to find all ${this.tableName}: ${response.error.message}`,
      );
    }

    return response.data || [];
  }

  async findById(id: string): Promise<T | null> {
    const response = await this.db.selectOne<T>(this.tableName, {
      filters: [{ column: "id", operator: "eq", value: id }],
    });

    if (response.error) {
      throw new Error(
        `Failed to find ${this.tableName} by id ${id}: ${response.error.message}`,
      );
    }

    return response.data;
  }

  async findOne(filters: QueryFilter[]): Promise<T | null> {
    const response = await this.db.selectOne<T>(this.tableName, { filters });

    if (response.error) {
      throw new Error(
        `Failed to find one ${this.tableName}: ${response.error.message}`,
      );
    }

    return response.data;
  }

  async findMany(filters: QueryFilter[], options?: QueryOptions): Promise<T[]> {
    const response = await this.db.select<T>(this.tableName, {
      ...options,
      filters: [...(options?.filters || []), ...filters],
    });

    if (response.error) {
      throw new Error(
        `Failed to find many ${this.tableName}: ${response.error.message}`,
      );
    }

    return response.data || [];
  }

  async create(data: Partial<T>): Promise<T> {
    const response = await this.db.insert<T>(this.tableName, data, {
      returning: "*",
    });

    if (response.error) {
      throw new Error(
        `Failed to create ${this.tableName}: ${response.error.message}`,
      );
    }

    if (!response.data) {
      throw new Error(`Failed to create ${this.tableName}: No data returned`);
    }

    // Handle both single object and array responses
    const result = Array.isArray(response.data)
      ? response.data[0]
      : response.data;
    return result as T;
  }

  async createMany(data: Partial<T>[]): Promise<T[]> {
    const response = await this.db.insert<T[]>(this.tableName, data, {
      returning: "*",
    });

    if (response.error) {
      throw new Error(
        `Failed to create many ${this.tableName}: ${response.error.message}`,
      );
    }

    return (response.data || []) as T[];
  }

  async update(id: string, data: Partial<T>): Promise<T | null> {
    const response = await this.db.update<T>(
      this.tableName,
      data,
      [{ column: "id", operator: "eq", value: id }],
      { returning: "*" },
    );

    if (response.error) {
      throw new Error(
        `Failed to update ${this.tableName} ${id}: ${response.error.message}`,
      );
    }

    // Handle both single object and array responses
    if (Array.isArray(response.data)) {
      return response.data.length > 0 ? response.data[0] : null;
    }

    return response.data;
  }

  async updateMany(filters: QueryFilter[], data: Partial<T>): Promise<T[]> {
    const response = await this.db.update<T[]>(this.tableName, data, filters, {
      returning: "*",
    });

    if (response.error) {
      throw new Error(
        `Failed to update many ${this.tableName}: ${response.error.message}`,
      );
    }

    return (response.data || []) as T[];
  }

  async delete(id: string): Promise<boolean> {
    const response = await this.db.delete(this.tableName, [
      { column: "id", operator: "eq", value: id },
    ]);

    if (response.error) {
      throw new Error(
        `Failed to delete ${this.tableName} ${id}: ${response.error.message}`,
      );
    }

    return true;
  }

  async deleteMany(filters: QueryFilter[]): Promise<number> {
    const response = await this.db.delete(this.tableName, filters, {
      returning: "*",
    });

    if (response.error) {
      throw new Error(
        `Failed to delete many ${this.tableName}: ${response.error.message}`,
      );
    }

    // Return count of deleted items
    if (Array.isArray(response.data)) {
      return response.data.length;
    }

    return response.data ? 1 : 0;
  }

  async count(filters?: QueryFilter[]): Promise<number> {
    const response = await this.db.select<T>(this.tableName, {
      select: "id",
      filters,
    });

    if (response.error) {
      throw new Error(
        `Failed to count ${this.tableName}: ${response.error.message}`,
      );
    }

    return response.count || response.data?.length || 0;
  }

  async exists(id: string): Promise<boolean> {
    const response = await this.db.selectOne<T>(this.tableName, {
      select: "id",
      filters: [{ column: "id", operator: "eq", value: id }],
    });

    if (response.error) {
      throw new Error(
        `Failed to check existence of ${this.tableName} ${id}: ${response.error.message}`,
      );
    }

    return response.data !== null;
  }

  // ============================================================================
  // Helper Methods
  // ============================================================================

  /**
   * Execute a custom RPC function
   */
  protected async rpc<R = any>(
    functionName: string,
    params?: Record<string, any>,
  ): Promise<R> {
    const response = await this.db.rpc<R>(functionName, params);

    if (response.error) {
      throw new Error(`RPC ${functionName} failed: ${response.error.message}`);
    }

    return response.data as R;
  }

  /**
   * Build common query options
   */
  protected buildQueryOptions(
    limit?: number,
    offset?: number,
    orderBy?: string,
    ascending?: boolean,
  ): QueryOptions {
    return {
      limit,
      offset,
      orderBy: orderBy
        ? [{ column: orderBy, ascending: ascending ?? true }]
        : undefined,
    };
  }

  /**
   * Build filter for active records (if the entity has is_active field)
   */
  protected activeFilter(): QueryFilter {
    return { column: "is_active", operator: "eq", value: true };
  }

  /**
   * Build filter for records by user ID
   */
  protected userFilter(userId: string): QueryFilter {
    return { column: "user_id", operator: "eq", value: userId };
  }

  /**
   * Build filter for records by role
   */
  protected roleFilter(role: string): QueryFilter {
    return { column: "role", operator: "eq", value: role };
  }
}
