name: Vercel Preview Deployment

on:
  push:
    branches-ignore:
      - main
      - develop

jobs:
  deploy-preview:
    runs-on: ubuntu-latest
    env:
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
      NEXT_DISABLE_ESLINT: 1
      NEXT_DISABLE_TYPESCRIPT: 1
      NODE_OPTIONS: --max_old_space_size=4096
    steps:
      - uses: actions/checkout@v3

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=preview --token=${VERCEL_TOKEN}

      - name: Build Project Artifacts
        run: vercel build --token=${VERCEL_TOKEN}

      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --token=${VERCEL_TOKEN}
