"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { ThemeToggle } from "@/components/theme/theme-toggle";

export function Navigation() {
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // No mostrar navegación en las rutas de panel o en la vista previa de certificados
  if (
    pathname.startsWith("/panel-admin") ||
    pathname.startsWith("/panel-alumno") ||
    pathname.startsWith("/certificado-preview")
  ) {
    return null;
  }

  return (
    <nav className="bg-background border-b shadow-sm">
      <div className="container mx-auto px-4 py-3">
        <div className="flex justify-between items-center">
          <div className="flex-shrink-0 flex items-center">
            <Link
              href="/"
              className="text-2xl font-bold text-primary flex items-center"
            >
              <span className="text-2xl font-bold text-foreground">
                QR CURSE
              </span>
            </Link>
          </div>
          <div className="hidden md:flex space-x-6">
            <Link
              href="/"
              className={`${
                pathname === "/"
                  ? "text-primary font-medium"
                  : "text-muted-foreground hover:text-primary"
              } transition-colors duration-300`}
            >
              Inicio
            </Link>
            <Link
              href="/cursos"
              className={`${
                pathname === "/cursos" || pathname.startsWith("/cursos/")
                  ? "text-primary font-medium"
                  : "text-muted-foreground hover:text-primary"
              } transition-colors duration-300`}
            >
              Cursos
            </Link>
            <Link
              href="/login"
              className={`${
                pathname === "/login"
                  ? "text-primary font-medium"
                  : "text-muted-foreground hover:text-primary"
              } transition-colors duration-300`}
            >
              Iniciar Sesión
            </Link>
            <Link
              href="/register"
              className={`${
                pathname === "/register"
                  ? "text-primary font-medium"
                  : "text-muted-foreground hover:text-primary"
              } transition-colors duration-300`}
            >
              Registrarse
            </Link>
            <Link
              href="/verificar-certificado"
              className={`${
                pathname === "/verificar-certificado"
                  ? "text-primary font-medium"
                  : "text-muted-foreground hover:text-primary"
              } transition-colors duration-300`}
            >
              Verificar Certificado
            </Link>
            <ThemeToggle />
          </div>
          <button
            className="md:hidden w-10 h-10 flex items-center justify-center text-muted-foreground"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            aria-expanded={mobileMenuOpen}
            aria-label={mobileMenuOpen ? "Cerrar menú" : "Abrir menú"}
          >
            <svg
              className="h-6 w-6"
              stroke="currentColor"
              fill="none"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              {mobileMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              )}
            </svg>
          </button>
        </div>
      </div>

      {mobileMenuOpen && (
        <div className="md:hidden absolute z-50 w-full bg-white shadow-lg rounded-b-lg">
          <div className="pt-2 pb-3 space-y-1 px-4">
            <Link
              href="/"
              className={`${
                pathname === "/"
                  ? "text-primary font-medium"
                  : "text-gray-700 hover:text-primary"
              } block py-2 transition-colors duration-300`}
            >
              Inicio
            </Link>
            <Link
              href="/cursos"
              className={`${
                pathname === "/cursos" || pathname.startsWith("/cursos/")
                  ? "text-primary font-medium"
                  : "text-gray-700 hover:text-primary"
              } block py-2 transition-colors duration-300`}
            >
              Cursos
            </Link>
            <Link
              href="/login"
              className={`${
                pathname === "/login"
                  ? "text-primary font-medium"
                  : "text-gray-700 hover:text-primary"
              } block py-2 transition-colors duration-300`}
            >
              Iniciar Sesión
            </Link>
            <Link
              href="/register"
              className={`${
                pathname === "/register"
                  ? "text-primary font-medium"
                  : "text-gray-700 hover:text-primary"
              } block py-2 transition-colors duration-300`}
            >
              Registrarse
            </Link>
            <Link
              href="/verificar-certificado"
              className={`${
                pathname === "/verificar-certificado"
                  ? "text-primary font-medium"
                  : "text-gray-700 hover:text-primary"
              } block py-2 transition-colors duration-300`}
            >
              Verificar Certificado
            </Link>
          </div>
        </div>
      )}
    </nav>
  );
}
