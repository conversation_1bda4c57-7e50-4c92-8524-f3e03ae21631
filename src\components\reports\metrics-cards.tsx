"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Award,
  Users,
  BookOpen,
  TrendingUp,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
} from "lucide-react";
import { type ReportMetrics } from "@/lib/data/mock-reports-data";

interface MetricsCardsProps {
  metrics: ReportMetrics;
  isLoading?: boolean;
}

export function MetricsCards({ metrics, isLoading = false }: MetricsCardsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 bg-muted rounded w-20"></div>
              <div className="h-4 w-4 bg-muted rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted rounded w-16 mb-2"></div>
              <div className="h-3 bg-muted rounded w-24"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const certificateSuccessRate = metrics.totalCertificates > 0 
    ? ((metrics.activeCertificates / metrics.totalCertificates) * 100).toFixed(1)
    : "0";

  const userActivityRate = metrics.totalUsers > 0
    ? ((metrics.activeUsers / metrics.totalUsers) * 100).toFixed(1)
    : "0";

  const courseCompletionRate = metrics.totalCourses > 0
    ? ((metrics.completedCourses / metrics.totalCourses) * 100).toFixed(1)
    : "0";

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Certificates */}
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Total Certificados
          </CardTitle>
          <Award className="h-4 w-4 text-primary" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-foreground">
            {metrics.totalCertificates.toLocaleString()}
          </div>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-1">
            <Badge variant="secondary" className="text-xs">
              {certificateSuccessRate}% activos
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Active Certificates */}
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Certificados Activos
          </CardTitle>
          <CheckCircle className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {metrics.activeCertificates.toLocaleString()}
          </div>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-1">
            <TrendingUp className="h-3 w-3" />
            <span>Válidos y vigentes</span>
          </div>
        </CardContent>
      </Card>

      {/* Total Users */}
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Total Usuarios
          </CardTitle>
          <Users className="h-4 w-4 text-blue-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">
            {metrics.totalUsers.toLocaleString()}
          </div>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-1">
            <Badge variant="outline" className="text-xs">
              {userActivityRate}% activos
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Total Courses */}
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Total Cursos
          </CardTitle>
          <BookOpen className="h-4 w-4 text-purple-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-purple-600">
            {metrics.totalCourses.toLocaleString()}
          </div>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-1">
            <Badge variant="outline" className="text-xs">
              {courseCompletionRate}% completados
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Expired Certificates */}
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Certificados Expirados
          </CardTitle>
          <Clock className="h-4 w-4 text-orange-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-orange-600">
            {metrics.expiredCertificates.toLocaleString()}
          </div>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-1">
            <AlertTriangle className="h-3 w-3" />
            <span>Requieren renovación</span>
          </div>
        </CardContent>
      </Card>

      {/* Revoked Certificates */}
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Certificados Revocados
          </CardTitle>
          <XCircle className="h-4 w-4 text-red-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">
            {metrics.revokedCertificates.toLocaleString()}
          </div>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-1">
            <span>Cancelados</span>
          </div>
        </CardContent>
      </Card>

      {/* Average Attendance */}
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Asistencia Promedio
          </CardTitle>
          <TrendingUp className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {metrics.averageAttendance.toFixed(1)}%
          </div>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-1">
            <span>Participación general</span>
          </div>
        </CardContent>
      </Card>

      {/* Average Grade */}
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Calificación Promedio
          </CardTitle>
          <Award className="h-4 w-4 text-yellow-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-yellow-600">
            {metrics.averageGrade.toFixed(1)}
          </div>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-1">
            <span>Sobre 10 puntos</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
