"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { use, useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";

// Define types
type Certificate = {
  id: string;
  certificate_number?: string;
  issue_date: string;
  expiry_date: string | null;
  status: string;
  qr_code?: string | null;
  qr_code_url?: string | null;
  course_id?: string;
  course?: {
    title: string;
    description: string | null;
  };
};

export default function StudentCertificateDetail({
  params,
}: {
  params: { id: string };
}) {
  const _router = useRouter();
  const unwrappedParams = use(params);
  const certificateId = unwrappedParams.id;

  // State
  const [certificate, setCertificate] = useState<Certificate | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [_userData, setUserData] = useState<{
    id: string;
    first_name: string;
    last_name: string;
  } | null>(null);

  // Status colors
  const statusColors = {
    ACTIVE: "bg-gray-300 text-gray-800",
    REVOKED: "bg-gray-400 text-gray-800",
    EXPIRED: "bg-gray-200 text-gray-800",
    active: "bg-gray-300 text-gray-800",
    revoked: "bg-gray-400 text-gray-800",
    expired: "bg-gray-200 text-gray-800",
  };

  const statusLabels = {
    ACTIVE: "Activo",
    REVOKED: "Revocado",
    EXPIRED: "Expirado",
    active: "Activo",
    revoked: "Revocado",
    expired: "Expirado",
  };

  // Fetch user data and certificate
  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        setError(null);

        // Get current user
        const {
          data: { user },
        } = await supabase.auth.getUser();

        if (!user) {
          throw new Error("No se pudo obtener información del usuario");
        }

        // Get user details
        const { data: userData, error: userError } = await supabase
          .from("users")
          .select("id, first_name, last_name")
          .eq("id", user.id)
          .single();

        if (userError) throw userError;
        setUserData(userData);

        // Fetch certificate
        const { data: certificateData, error: certificateError } =
          await supabase
            .from("certificates")
            .select(`
            id,
            issue_date,
            expiry_date,
            status,
            qr_code,
            qr_code_url,
            course_id
          `)
            .eq("id", certificateId)
            .eq("user_id", user.id)
            .single();

        if (certificateError) {
          // If error is "No rows found", it means the certificate doesn't exist or doesn't belong to the current user
          if (certificateError.code === "PGRST116") {
            throw new Error(
              "El certificado solicitado no existe o no tienes permiso para verlo",
            );
          }
          throw certificateError;
        }

        // Fetch course data if we have a certificate
        if (certificateData?.course_id) {
          const { data: courseData, error: courseError } = await supabase
            .from("courses")
            .select("id, title, description")
            .eq("id", certificateData.course_id)
            .single();

          if (!courseError && courseData) {
            certificateData.course = courseData;
          }
        }

        setCertificate(certificateData);
      } catch (error: any) {
        console.error("Error fetching certificate:", error);
        setError(error.message || "Error al cargar el certificado");
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [certificateId]);

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-gray-500">Cargando certificado...</p>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-red-50 border-l-4 border-red-500 p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
            <div className="mt-4">
              <Link
                href="/panel-alumno/certificados"
                className="text-sm text-red-700 font-medium hover:text-red-600"
              >
                ← Volver a mis certificados
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // No certificate found
  if (!certificate) {
    return (
      <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Certificado no encontrado
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>
                El certificado solicitado no existe o no tienes permiso para
                verlo.
              </p>
            </div>
            <div className="mt-4">
              <Link
                href="/panel-alumno/certificados"
                className="text-sm text-yellow-700 font-medium hover:text-yellow-600"
              >
                ← Volver a mis certificados
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header with actions */}
      <div className="md:flex md:items-center md:justify-between mb-6">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Certificado: {certificate.course?.title || "Curso sin nombre"}
          </h2>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <Link
            href="/panel-alumno/certificados"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            ← Volver
          </Link>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Información del Certificado
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Detalles de tu certificado de{" "}
              {certificate.course?.title || "Curso sin nombre"}.
            </p>
          </div>
          <div>
            <span
              className={`inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium ${
                statusColors[certificate.status as keyof typeof statusColors] ||
                "bg-gray-100 text-gray-800"
              }`}
            >
              {statusLabels[certificate.status as keyof typeof statusLabels] ||
                certificate.status}
            </span>
          </div>
        </div>

        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Número de Certificado
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {certificate.certificate_number ||
                  `ID: ${certificate.id.substring(0, 8)}...`}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Curso</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {certificate.course?.title || "Curso sin nombre"}
                {certificate.course?.description && (
                  <p className="text-xs text-gray-500 mt-1">
                    {certificate.course.description}
                  </p>
                )}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Fecha de Emisión
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {new Date(certificate.issue_date).toLocaleDateString("es-ES", {
                  day: "2-digit",
                  month: "long",
                  year: "numeric",
                })}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Fecha de Vencimiento
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {certificate.expiry_date
                  ? new Date(certificate.expiry_date).toLocaleDateString(
                      "es-ES",
                      {
                        day: "2-digit",
                        month: "long",
                        year: "numeric",
                      },
                    )
                  : "No vence"}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Estado</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <span
                  className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    statusColors[
                      certificate.status as keyof typeof statusColors
                    ] || "bg-gray-100 text-gray-800"
                  }`}
                >
                  {statusLabels[
                    certificate.status as keyof typeof statusLabels
                  ] || certificate.status}
                </span>
                {certificate.status === "REVOKED" ||
                  (certificate.status === "revoked" && (
                    <p className="mt-1 text-sm text-gray-600">
                      Este certificado ha sido revocado y ya no es válido.
                    </p>
                  ))}
                {certificate.status === "EXPIRED" ||
                  (certificate.status === "expired" && (
                    <p className="mt-1 text-sm text-gray-600">
                      Este certificado ha expirado y ya no es válido.
                    </p>
                  ))}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Enlace de Verificación
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <a
                  href={`${window.location.origin}/verificar-certificado/certificado/${certificate.id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-700 hover:text-gray-900"
                >
                  {`${window.location.origin}/verificar-certificado/certificado/${certificate.id}`}
                </a>
                <p className="mt-1 text-xs text-gray-500">
                  Este enlace permite a cualquier persona verificar la
                  autenticidad de tu certificado.
                </p>
              </dd>
            </div>
          </dl>
        </div>

        {/* QR Curse */}
        {(certificate.qr_code || certificate.qr_code_url) && (
          <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Código QR
            </h3>
            <div className="flex justify-center">
              <div className="p-4 bg-white border border-gray-200 rounded-lg">
                <img
                  src={certificate.qr_code || certificate.qr_code_url}
                  alt="QR Curse"
                  className="w-48 h-48"
                />
              </div>
            </div>
            <div className="mt-4 text-center">
              <p className="text-sm text-gray-500">
                Este código QR dirige a la página de verificación del
                certificado.
              </p>
              <div className="mt-4 flex justify-center space-x-4">
                <a
                  href={certificate.qr_code || certificate.qr_code_url}
                  download={`certificado-${certificate.id.substring(0, 8)}.png`}
                  className={`inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md ${
                    certificate.status === "ACTIVE" ||
                    certificate.status === "active"
                      ? "text-white bg-gray-700 hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-600"
                      : "text-gray-500 bg-gray-200 cursor-not-allowed"
                  }`}
                  onClick={(e) => {
                    if (
                      certificate.status !== "ACTIVE" &&
                      certificate.status !== "active"
                    ) {
                      e.preventDefault();
                    }
                  }}
                >
                  Descargar QR
                </a>
                <a
                  href={`${window.location.origin}/verificar-certificado/certificado/${certificate.id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-700 hover:text-gray-900"
                >
                  Verificar Certificado
                </a>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
