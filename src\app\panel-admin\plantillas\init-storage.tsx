"use client";

import { AlertCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ensureBucketExists } from "@/lib/storage-utils";

export default function InitStorage() {
  const [status, setStatus] = useState<
    "loading" | "success" | "error" | "warning"
  >("loading");
  const [message, setMessage] = useState<string>(
    "Verificando almacenamiento...",
  );

  useEffect(() => {
    async function initStorage() {
      try {
        // Verificar y crear el bucket de firmas si no existe
        const result = await ensureBucketExists("signatures", true);

        if (result.success) {
          // Si el mensaje contiene "permisos limitados", mostramos una advertencia
          if (result.message?.includes("permisos limitados")) {
            setStatus("warning");
            setMessage(
              "Acceso limitado al almacenamiento. Las firmas pueden no funcionar correctamente.",
            );
            console.warn("Acceso limitado al almacenamiento:", result.message);
          } else {
            setStatus("success");
            setMessage(
              result.created
                ? "Bucket de firmas creado exitosamente."
                : "Bucket de firmas verificado correctamente.",
            );
          }
        } else {
          // Si el error es de permisos, mostramos una advertencia en lugar de un error
          if (
            result.error?.message &&
            (result.error.message.includes("permission") ||
              result.error.message.includes("security policy") ||
              result.error.message.includes("not authorized"))
          ) {
            setStatus("warning");
            setMessage(
              "Permisos limitados para el almacenamiento. Las firmas pueden no funcionar correctamente.",
            );
            console.warn(
              "Permisos limitados para el almacenamiento:",
              result.message,
            );
          } else {
            setStatus("error");
            setMessage(
              `Error al verificar/crear bucket de firmas: ${result.message}`,
            );
            console.error(
              "Error al verificar/crear bucket de firmas:",
              result.error,
            );
          }
        }
      } catch (error: any) {
        setStatus("error");
        setMessage(`Error inesperado: ${error.message || "Error desconocido"}`);
        console.error("Error inesperado al inicializar almacenamiento:", error);
      }
    }

    initStorage();
  }, []);

  if (status === "loading") {
    return null; // No mostrar nada durante la carga
  }

  if (status === "error") {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error de almacenamiento</AlertTitle>
        <AlertDescription>
          {message}
          <div className="mt-2 text-xs">
            Es posible que no puedas subir firmas. Contacta al administrador.
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  if (status === "warning") {
    return (
      <Alert variant="warning" className="mb-4 border-yellow-500 bg-yellow-50">
        <AlertCircle className="h-4 w-4 text-yellow-600" />
        <AlertTitle className="text-yellow-700">
          Advertencia de almacenamiento
        </AlertTitle>
        <AlertDescription className="text-yellow-600">
          {message}
          <div className="mt-2 text-xs">
            Puedes continuar usando la aplicación, pero algunas funciones pueden
            estar limitadas.
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // En caso de éxito, no mostramos nada para no distraer al usuario
  return null;
}
