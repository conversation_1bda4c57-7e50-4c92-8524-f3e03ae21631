/**
 * @fileoverview Integration tests for service layer
 *
 * Tests the integration between services and repositories with mocked dependencies.
 * These tests verify that the service layer correctly orchestrates business logic.
 */

import type { RepositoryFactory } from "../../repositories";
import { BaseService, type ServiceResponse } from "../base-service";

// Mock repository factory
const mockRepositoryFactory = {} as RepositoryFactory;

// Test implementation of BaseService for integration testing
class TestIntegrationService extends BaseService {
  constructor() {
    super(mockRepositoryFactory);
  }

  // Test method that uses multiple BaseService features
  async testComplexOperation(data: {
    email: string;
    name: string;
    age?: number;
  }): Promise<ServiceResponse<{ id: string; processedData: any }>> {
    // Sanitize inputs first
    const sanitizedEmail = this.sanitizeString(data.email);
    const sanitizedName = this.sanitizeString(data.name);

    // Validate required fields
    const emailValidation = this.validateEmail(sanitizedEmail);
    if (emailValidation) return emailValidation;

    const nameValidation = this.validateRequired(sanitizedName, "name");
    if (nameValidation) return nameValidation;

    const lengthValidation = this.validateLength(sanitizedName, "name", 2, 50);
    if (lengthValidation) return lengthValidation;

    // Simulate processing
    try {
      const processedData = {
        email: sanitizedEmail,
        name: sanitizedName,
        age: data.age || 0,
        processedAt: new Date().toISOString(),
        id: this.generateId(),
      };

      return this.success(
        {
          id: processedData.id,
          processedData,
        },
        "Data processed successfully",
      );
    } catch (error) {
      return this.error("PROCESSING_ERROR", "Failed to process data", error);
    }
  }

  // Test pagination functionality
  async testPaginatedQuery(
    options: { page?: number; limit?: number; search?: string } = {},
  ): Promise<
    ServiceResponse<{
      data: any[];
      pagination: any;
    }>
  > {
    const { page, limit, offset } = this.validatePaginationOptions(options);

    // Simulate data retrieval
    const mockData = Array.from({ length: 25 }, (_, i) => ({
      id: `item-${i + 1}`,
      name: `Item ${i + 1}`,
      active: i % 2 === 0,
    }));

    // Apply search filter if provided
    let filteredData = mockData;
    if (options.search) {
      filteredData = mockData.filter((item) =>
        item.name.toLowerCase().includes(options.search?.toLowerCase()),
      );
    }

    // Apply pagination
    const paginatedData = filteredData.slice(offset, offset + limit);
    const pagination = this.calculatePagination(
      filteredData.length,
      page,
      limit,
    );

    return this.success({
      data: paginatedData,
      pagination,
    });
  }

  // Test error handling
  async testErrorScenarios(scenario: string): Promise<ServiceResponse<any>> {
    switch (scenario) {
      case "validation":
        return this.validationError("test_field", "Test validation error");

      case "not_found":
        return this.notFoundError("TestEntity", "test-id");

      case "conflict":
        return this.conflictError("Test conflict error");

      case "unauthorized":
        return this.unauthorizedError("Test unauthorized error");

      case "server_error":
        return this.error("SERVER_ERROR", "Test server error");

      default:
        return this.success({ scenario }, "Test completed");
    }
  }
}

describe("Service Integration Tests", () => {
  let service: TestIntegrationService;

  beforeEach(() => {
    service = new TestIntegrationService();
  });

  describe("Complex Operation Integration", () => {
    it("should successfully process valid data", async () => {
      const testData = {
        email: "<EMAIL>",
        name: "John Doe",
        age: 30,
      };

      const result = await service.testComplexOperation(testData);

      expect(result.success).toBe(true);
      expect(result.data?.processedData.email).toBe("<EMAIL>");
      expect(result.data?.processedData.name).toBe("John Doe");
      expect(result.data?.processedData.age).toBe(30);
      expect(result.data?.id).toMatch(
        /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
      );
      expect(result.message).toBe("Data processed successfully");
    });

    it("should handle validation errors in complex operations", async () => {
      const testData = {
        email: "invalid-email",
        name: "John Doe",
      };

      const result = await service.testComplexOperation(testData);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe("VALIDATION_ERROR");
      expect(result.error?.field).toBe("email");
    });

    it("should validate required fields in complex operations", async () => {
      const testData = {
        email: "<EMAIL>",
        name: "",
      };

      const result = await service.testComplexOperation(testData);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe("VALIDATION_ERROR");
      expect(result.error?.field).toBe("name");
    });

    it("should validate field length in complex operations", async () => {
      const testData = {
        email: "<EMAIL>",
        name: "A", // Too short
      };

      const result = await service.testComplexOperation(testData);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe("VALIDATION_ERROR");
      expect(result.error?.field).toBe("name");
    });
  });

  describe("Pagination Integration", () => {
    it("should handle paginated queries with default options", async () => {
      const result = await service.testPaginatedQuery();

      expect(result.success).toBe(true);
      expect(result.data?.data).toHaveLength(10); // Default limit
      expect(result.data?.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 25,
        totalPages: 3,
        hasNext: true,
        hasPrev: false,
      });
    });

    it("should handle paginated queries with custom options", async () => {
      const result = await service.testPaginatedQuery({
        page: 2,
        limit: 5,
      });

      expect(result.success).toBe(true);
      expect(result.data?.data).toHaveLength(5);
      expect(result.data?.pagination).toEqual({
        page: 2,
        limit: 5,
        total: 25,
        totalPages: 5,
        hasNext: true,
        hasPrev: true,
      });
    });

    it("should handle search in paginated queries", async () => {
      const result = await service.testPaginatedQuery({
        search: "Item 1",
        limit: 20,
      });

      expect(result.success).toBe(true);
      expect(result.data?.data.length).toBeGreaterThan(0);
      expect(
        result.data?.data.every((item) => item.name.includes("Item 1")),
      ).toBe(true);
    });

    it("should handle empty search results", async () => {
      const result = await service.testPaginatedQuery({
        search: "NonexistentItem",
      });

      expect(result.success).toBe(true);
      expect(result.data?.data).toHaveLength(0);
      expect(result.data?.pagination.total).toBe(0);
    });
  });

  describe("Error Handling Integration", () => {
    it("should handle validation errors", async () => {
      const result = await service.testErrorScenarios("validation");

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe("VALIDATION_ERROR");
      expect(result.error?.field).toBe("test_field");
      expect(result.error?.message).toBe("Test validation error");
    });

    it("should handle not found errors", async () => {
      const result = await service.testErrorScenarios("not_found");

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe("NOT_FOUND");
      expect(result.error?.message).toBe(
        "TestEntity with id test-id not found",
      );
    });

    it("should handle conflict errors", async () => {
      const result = await service.testErrorScenarios("conflict");

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe("CONFLICT");
      expect(result.error?.message).toBe("Test conflict error");
    });

    it("should handle unauthorized errors", async () => {
      const result = await service.testErrorScenarios("unauthorized");

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe("UNAUTHORIZED");
      expect(result.error?.message).toBe("Test unauthorized error");
    });

    it("should handle server errors", async () => {
      const result = await service.testErrorScenarios("server_error");

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe("SERVER_ERROR");
      expect(result.error?.message).toBe("Test server error");
    });

    it("should handle successful scenarios", async () => {
      const result = await service.testErrorScenarios("success");

      expect(result.success).toBe(true);
      expect(result.data?.scenario).toBe("success");
      expect(result.message).toBe("Test completed");
    });
  });

  describe("Data Sanitization Integration", () => {
    it("should sanitize strings in complex operations", async () => {
      const testData = {
        email: "  <EMAIL>  ",
        name: "  John   Doe  \n\n  ",
      };

      const result = await service.testComplexOperation(testData);

      expect(result.success).toBe(true);
      expect(result.data?.processedData.email).toBe("<EMAIL>");
      expect(result.data?.processedData.name).toBe("John Doe");
    });
  });

  describe("ID Generation Integration", () => {
    it("should generate unique IDs in complex operations", async () => {
      const testData = {
        email: "<EMAIL>",
        name: "John Doe",
      };

      const results = await Promise.all([
        service.testComplexOperation(testData),
        service.testComplexOperation(testData),
        service.testComplexOperation(testData),
      ]);

      const ids = results.map((r) => r.data?.id);
      const uniqueIds = new Set(ids);

      expect(uniqueIds.size).toBe(3); // All IDs should be unique
      ids.forEach((id) => {
        expect(id).toMatch(
          /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
        );
      });
    });
  });
});
