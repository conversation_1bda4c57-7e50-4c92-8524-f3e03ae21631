/**
 * MCP Client - Utility functions for interacting with the MCP Server
 *
 * This module provides functions to interact with the MCP Server for various operations
 * like course content management, user management, etc.
 */

/**
 * Fetch course content data from the MCP Server
 * @param courseId - The ID of the course to fetch content for
 * @param section - Optional section to fetch (content, objectives, legalFramework)
 * @returns The course content data including content, objectives, and legal framework
 */
export async function fetchCourseContent(courseId: string, section?: string) {
  try {
    // Crear URL con parámetros
    const url = new URL(`/api/admin/course-content`, window.location.origin);
    url.searchParams.append("courseId", courseId);
    if (section) {
      url.searchParams.append("section", section);
    }

    // Configurar opciones de caché para mejorar el rendimiento
    const cacheOptions: RequestInit = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      // Usar caché para mejorar el rendimiento
      cache: "default",
      next: {
        revalidate: 60, // Revalidar cada 60 segundos
      } as any,
    };

    const response = await fetch(url.toString(), cacheOptions);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Error fetching course content");
    }

    return await response.json();
  } catch (error: any) {
    console.error("Error in fetchCourseContent:", error);
    throw error;
  }
}

/**
 * Save course content data through the MCP Server
 * @param courseId - The ID of the course to save content for
 * @param content - The course content data
 * @param objectives - The course objectives data
 * @param legalFramework - The course legal framework data
 * @returns The response from the server
 */
export async function saveCourseContent(
  courseId: string,
  content: any[],
  objectives: any[],
  legalFramework: any[],
) {
  try {
    const response = await fetch("/api/admin/course-content", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        courseId,
        content,
        objectives,
        legalFramework,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Error saving course content");
    }

    return await response.json();
  } catch (error: any) {
    console.error("Error in saveCourseContent:", error);
    throw error;
  }
}
