# 🎨 Design System Guide

> **See Also:** [INDEX.md](./INDEX.md) | [PLANNING.md](./PLANNING.md) | [RULES.md](./RULES.md)

This document describes the design system for the QR Course Platform Scaffold, including component usage, branding, accessibility, and customization.

---

## 1. Brand & Theme

- **Primary Color:** #003366 (CSI Blue)
- **Accent Color:** #F9B233 (CSI Yellow)
- **Dark:** #222222
- **White:** #FFFFFF
- **Fonts:**
  - Headings: Montserrat (Google Fonts)
  - Body: Open Sans (Google Fonts)

Override these in `tailwind.config.js` and `globals.css` for new clients.

---

## 2. Component Library

- **Base:** [Shadcn/ui](https://ui.shadcn.com/), [Radix UI](https://www.radix-ui.com/)
- **Custom Components:**
  - <PERSON><PERSON>, Card, Table, Tabs, Toast, Dialog, Badge, QR Curse, etc.
- **Atomic Design:** Components are atomic and composable.
- **Usage:**
  - Import from `src/components/ui/` or `src/components/`.
  - All components are accessible and responsive by default.

---

## 3. Customization

- **Colors:**
  - Use `bg-csi-blue`, `text-csi-yellow`, etc. (see Tailwind config)
- **Fonts:**
  - Use `font-heading` for headings, `font-body` for body text
- **Logos:**
  - Place SVG/PNG logos in `public/images/` and reference in layouts
- **Theme:**
  - Light/Dark mode ready (extend as needed)

---

## 4. Accessibility

- **WCAG 2.1** compliant
- Keyboard navigation for all interactive elements
- Sufficient color contrast
- Use semantic HTML and ARIA attributes

---

## 5. Responsive Design

- Mobile-first, desktop-optimized
- Use Tailwind's responsive utilities (`md:`, `lg:`, etc.)

---

## 6. Extending the System

- Add new components to `src/components/ui/` or `src/components/`
- Follow atomic/component-driven design
- Document new components in this file

---

## 7. Example Usage

```tsx
<button className="button-primary font-heading">Get Started</button>
<h1 className="text-csi-blue font-heading">Welcome</h1>
<p className="font-body text-csi-dark">This is a sample paragraph.</p>
```

---

## 8. Resources

- [Shadcn/ui Docs](https://ui.shadcn.com/docs)
- [Radix UI Docs](https://www.radix-ui.com/docs)
- [Tailwind CSS Docs](https://tailwindcss.com/docs)
- [Google Fonts: Montserrat](https://fonts.google.com/specimen/Montserrat)
- [Google Fonts: Open Sans](https://fonts.google.com/specimen/Open+Sans)

---

## Modernization Roadmap Reference

- See the [README](../README.md#-plan-de-modernización-y-mejora-continua-faseshitos) for the full phased plan.
- This design system is Phase 1 of the modernization roadmap.
- All branding and design practices are cross-referenced in [PLANNING.md](./PLANNING.md), [INDEX.md](./INDEX.md), and [RULES.md](./RULES.md).