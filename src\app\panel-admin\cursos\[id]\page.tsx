"use client";

import Link from "next/link";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import CourseContentManager from "@/components/courses/CourseContentManager";
import CourseInstructorSelector from "@/components/courses/CourseInstructorSelector";
import CourseLegalFrameworkManager from "@/components/courses/CourseLegalFrameworkManager";
import CourseObjectivesManager from "@/components/courses/CourseObjectivesManager";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/lib/supabase";

export default function DetalleCursoPage() {
  const params = useParams();
  const router = useRouter();
  const courseId = params.id as string;

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [course, setCourse] = useState<any>(null);
  const [_instructors, _setInstructors] = useState<any[]>([]);
  const [templates, setTemplates] = useState<any[]>([]);
  const [certificates, setCertificates] = useState<any[]>([]);
  // Commented out unused state
  // const [students, setStudents] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    code: "",
    hours: "", // Añadido: estado para horas (string para input)
    instructor_id: "",
    certificate_template_id: "",
    status: "",
  });

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        setError(null);

        // Fetch course details including hours
        const { data: courseData, error: courseError } = await supabase
          .from("courses")
          .select(`
            *,
            hours,
            instructor:instructor_id(id, first_name, last_name),
            certificate_template:certificate_template_id(id, name)
          `) // Corregida la consulta select
          .eq("id", courseId)
          .single();

        if (courseError) {
          if (courseError.code === "PGRST116") {
            // No data found error
            throw new Error(
              `No se encontró el curso con ID: ${courseId}. Es posible que haya sido eliminado o que la URL sea incorrecta.`,
            );
          }
          throw courseError;
        }

        if (!courseData) {
          throw new Error(
            `No se encontró el curso con ID: ${courseId}. Es posible que haya sido eliminado o que la URL sea incorrecta.`,
          );
        }

        setCourse(courseData);
        setFormData({
          title: courseData.title || "",
          description: courseData.description || "",
          code: courseData.code || "",
          // Añadir hours al estado inicial, convirtiendo null a string vacío
          hours: courseData.hours !== null ? courseData.hours.toString() : "",
          instructor_id: courseData.instructor_id || "none",
          certificate_template_id:
            courseData.certificate_template_id || "default",
          status: courseData.status || "draft",
        }); // Asegurarse que las propiedades se lean correctamente

        // Ya no necesitamos cargar instructores aquí, se manejan en el componente CourseInstructorSelector

        // Fetch certificate templates
        const { data: templatesData, error: templatesError } = await supabase
          .from("certificate_templates")
          .select("id, name")
          .order("name", { ascending: true });

        if (templatesError) throw templatesError;
        setTemplates(templatesData || []);

        // Fetch certificates for this course
        const { data: certificatesData, error: certificatesError } =
          await supabase
            .from("certificates")
            .select(`
            *,
            user:user_id(id, first_name, last_name, email)
          `)
            .eq("course_id", courseId)
            .order("issue_date", { ascending: false });

        if (certificatesError) throw certificatesError;
        setCertificates(certificatesData || []);

        // Fetch students code commented out as it's not currently used
        // Keeping this commented for future reference if needed
        /*
        const { data: studentsData, error: studentsError } = await supabase
          .from("users")
          .select("id, first_name, last_name, email")
          .eq("role", "student")
          .order("first_name", { ascending: true });

        if (studentsError) throw studentsError;
        setStudents(studentsData || []);
        */
      } catch (error: any) {
        console.error("Error fetching course data:", error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    }

    if (courseId) {
      fetchData();
    }
  }, [courseId]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Referencias a los componentes de gestión
  const objectivesManagerRef = useRef<any>(null);
  const legalFrameworkManagerRef = useRef<any>(null);
  const contentManagerRef = useRef<any>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);

      // Preparar los datos asegurándose de que hours sea un número entero o null
      const dataToUpdate = {
        ...formData,
        hours:
          formData.hours === "" || Number.isNaN(parseInt(formData.hours))
            ? null
            : parseInt(formData.hours, 10),
        instructor_id:
          formData.instructor_id === "none" ? null : formData.instructor_id,
        certificate_template_id:
          formData.certificate_template_id === "default"
            ? null
            : formData.certificate_template_id,
      };

      // Validar que hours sea un número positivo si no es null
      if (dataToUpdate.hours !== null && dataToUpdate.hours < 0) {
        toast({
          title: "Error de validación",
          description: "Las horas deben ser un número positivo.",
          variant: "destructive",
        });
        setSaving(false);
        return;
      }

      // Actualizar la información básica del curso
      const { error } = await supabase
        .from("courses")
        .update(dataToUpdate)
        .eq("id", courseId);

      if (error) throw error;

      // Guardar objetivos del curso
      if (
        objectivesManagerRef.current &&
        typeof objectivesManagerRef.current.saveObjectives === "function"
      ) {
        const objectivesSaved =
          await objectivesManagerRef.current.saveObjectives();
        if (!objectivesSaved) {
          throw new Error("No se pudieron guardar los objetivos del curso");
        }
      }

      // Guardar marco legal del curso
      if (
        legalFrameworkManagerRef.current &&
        typeof legalFrameworkManagerRef.current.saveLegalFrameworks ===
          "function"
      ) {
        const legalFrameworkSaved =
          await legalFrameworkManagerRef.current.saveLegalFrameworks();
        if (!legalFrameworkSaved) {
          throw new Error("No se pudo guardar el marco legal del curso");
        }
      }

      // Guardar contenido del curso
      if (
        contentManagerRef.current &&
        typeof contentManagerRef.current.saveContents === "function"
      ) {
        const contentSaved = await contentManagerRef.current.saveContents();
        if (!contentSaved) {
          throw new Error("No se pudo guardar el contenido del curso");
        }
      }

      toast({
        title: "Curso actualizado",
        description: "Todos los cambios se han guardado correctamente.",
      });

      // Actualizar el estado local del curso para reflejar los cambios inmediatamente
      setCourse((prev: any) => ({ ...prev, ...dataToUpdate }));

      // Opcional: Refrescar datos desde el servidor si es necesario
      // router.refresh();
    } catch (error: any) {
      console.error("Error updating course:", error);
      toast({
        title: "Error",
        description: `No se pudo actualizar el curso. ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "published":
        return <Badge className="bg-green-500">Publicado</Badge>;
      case "draft":
        return <Badge className="bg-yellow-500">Borrador</Badge>;
      case "archived":
        return <Badge className="bg-gray-500">Archivado</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <Skeleton className="h-10 w-1/3 mb-6" />
        <Tabs defaultValue="details">
          <TabsList className="mb-4">
            <Skeleton className="h-10 w-24 mr-2" />
            <Skeleton className="h-10 w-24 mr-2" />
            <Skeleton className="h-10 w-24" />
          </TabsList>
          <Skeleton className="h-96 w-full" />
        </Tabs>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
          <h3 className="text-lg font-medium">Error al cargar el curso</h3>
          <p>{error}</p>
          <div className="mt-4">
            <p className="text-sm text-gray-700 mb-2">Posibles soluciones:</p>
            <ul className="list-disc list-inside text-sm text-gray-700 mb-4">
              <li>Verifica que la URL sea correcta</li>
              <li>El curso puede haber sido eliminado</li>
              <li>Puede haber un problema de conexión con la base de datos</li>
            </ul>
            <Button
              variant="outline"
              onClick={() => router.push("/panel-admin/cursos")}
            >
              Volver a la lista de cursos
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">{course.title}</h1>
          {/* Modificado: Mostrar código, horas y estado */}
          <div className="flex items-center mt-1 flex-wrap gap-x-2 gap-y-1">
            <span className="text-sm text-gray-500">
              Código: {course.code || "N/A"}
            </span>
            {course.hours !== null && (
              <span className="text-sm text-gray-500">
                Horas: {course.hours}
              </span>
            )}
            {getStatusBadge(course.status)}
          </div>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push("/panel-admin/cursos")}
        >
          Volver a la lista
        </Button>
      </div>

      <Tabs
        defaultValue="details"
        onValueChange={async (_value) => {
          // Save data when switching tabs
          const currentTab = document
            .querySelector('[data-state="active"][data-radix-collection-item]')
            ?.getAttribute("value");

          // Only save if we're switching from a tab that needs saving
          if (
            currentTab === "objectives" &&
            objectivesManagerRef.current?.saveObjectives
          ) {
            await objectivesManagerRef.current.saveObjectives();
          } else if (
            currentTab === "legal" &&
            legalFrameworkManagerRef.current?.saveLegalFrameworks
          ) {
            await legalFrameworkManagerRef.current.saveLegalFrameworks();
          } else if (
            currentTab === "content" &&
            contentManagerRef.current?.saveContents
          ) {
            await contentManagerRef.current.saveContents();
          }
        }}
      >
        <TabsList className="mb-4">
          <TabsTrigger value="details">Detalles del Curso</TabsTrigger>
          <TabsTrigger value="objectives">Objetivos</TabsTrigger>
          <TabsTrigger value="legal">Marco Legal</TabsTrigger>
          <TabsTrigger value="content">Contenido</TabsTrigger>
          <TabsTrigger value="certificates">
            Certificados ({certificates.length})
          </TabsTrigger>
          <TabsTrigger value="template">Plantilla de Certificado</TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>Información del Curso</CardTitle>
              <CardDescription>Edita los detalles del curso</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Título del Curso</Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Descripción</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={4}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="code">Código del Curso</Label>
                  <Input
                    id="code"
                    name="code"
                    value={formData.code}
                    onChange={handleChange}
                  />
                </div>

                {/* Añadido: Campo para Horas */}
                <div className="space-y-2">
                  <Label htmlFor="hours">Horas del Curso</Label>
                  <Input
                    id="hours"
                    name="hours"
                    type="number"
                    value={formData.hours}
                    onChange={handleChange}
                    placeholder="Ej: 40"
                    min="0"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="instructor_id">Instructor Principal</Label>
                  <div className="pt-1">
                    {courseId && (
                      <CourseInstructorSelector
                        courseId={courseId}
                        currentInstructorId={formData.instructor_id}
                        onInstructorChange={(value) =>
                          handleSelectChange("instructor_id", value)
                        }
                      />
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="certificate_template_id">
                    Plantilla de Certificado
                  </Label>
                  <Select
                    value={formData.certificate_template_id}
                    onValueChange={(value) =>
                      handleSelectChange("certificate_template_id", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona una plantilla" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">
                        Plantilla predeterminada
                      </SelectItem>
                      {templates.map((template) => (
                        <SelectItem key={template.id} value={template.id}>
                          {template.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Estado</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) =>
                      handleSelectChange("status", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona un estado" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Borrador</SelectItem>
                      <SelectItem value="published">Publicado</SelectItem>
                      <SelectItem value="archived">Archivado</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex justify-end pt-4">
                  <Button type="submit" disabled={saving}>
                    {saving ? "Guardando..." : "Guardar Cambios"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="certificates">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Certificados Emitidos</CardTitle>
                <Link
                  href={`/panel-admin/certificados/nuevo?curso=${courseId}`}
                >
                  <Button>Emitir Nuevo Certificado</Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              {certificates.length === 0 ? (
                <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-md p-4">
                  <p>No hay certificados emitidos para este curso.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="px-4 py-2 text-left">Estudiante</th>
                        <th className="px-4 py-2 text-left">Número</th>
                        <th className="px-4 py-2 text-left">
                          Fecha de Emisión
                        </th>
                        <th className="px-4 py-2 text-left">Estado</th>
                        <th className="px-4 py-2 text-left">Acciones</th>
                      </tr>
                    </thead>
                    <tbody>
                      {certificates.map((cert) => (
                        <tr key={cert.id} className="border-t">
                          <td className="px-4 py-2">
                            {cert.user
                              ? `${cert.user.first_name} ${cert.user.last_name}`
                              : "N/A"}
                          </td>
                          <td className="px-4 py-2">
                            {cert.certificate_number}
                          </td>
                          <td className="px-4 py-2">
                            {new Date(cert.issue_date).toLocaleDateString(
                              "es-ES",
                            )}
                          </td>
                          <td className="px-4 py-2">
                            <Badge
                              className={
                                cert.status === "active"
                                  ? "bg-green-500"
                                  : "bg-gray-500"
                              }
                            >
                              {cert.status === "active"
                                ? "Activo"
                                : cert.status}
                            </Badge>
                          </td>
                          <td className="px-4 py-2">
                            <div className="flex space-x-2">
                              <Link
                                href={`/certificado/${cert.id}`}
                                target="_blank"
                              >
                                <Button variant="outline" size="sm">
                                  Ver
                                </Button>
                              </Link>
                              <Link
                                href={`/panel-admin/certificados/${cert.id}`}
                              >
                                <Button variant="outline" size="sm">
                                  Editar
                                </Button>
                              </Link>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="objectives">
          <CourseObjectivesManager
            ref={objectivesManagerRef}
            courseId={courseId}
          />
        </TabsContent>

        <TabsContent value="legal">
          <CourseLegalFrameworkManager
            ref={legalFrameworkManagerRef}
            courseId={courseId}
          />
        </TabsContent>

        <TabsContent value="content">
          <CourseContentManager ref={contentManagerRef} courseId={courseId} />
        </TabsContent>

        <TabsContent value="template">
          <Card>
            <CardHeader>
              <CardTitle>Plantilla de Certificado</CardTitle>
              <CardDescription>
                {course.certificate_template
                  ? `Usando plantilla: ${course.certificate_template.name}`
                  : "Usando plantilla predeterminada"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col space-y-4">
                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium mb-2">Previsualización</h3>
                  <div className="aspect-video bg-white border rounded-md overflow-hidden">
                    {course.certificate_template_id ? (
                      <iframe
                        src={`/certificado-preview/${course.certificate_template_id}`}
                        className="w-full h-full border-0"
                        title="Vista previa del certificado"
                        scrolling="no"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <p className="text-gray-500">
                          Usando plantilla predeterminada. Seleccione una
                          plantilla personalizada para ver la previsualización.
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex justify-between">
                  <Link href="/panel-admin/plantillas">
                    <Button variant="outline">Gestionar Plantillas</Button>
                  </Link>

                  {course.certificate_template_id ? (
                    <Link
                      href={`/certificado-preview/${course.certificate_template_id}?curso=${courseId}`}
                      target="_blank"
                    >
                      <Button>Ver Ejemplo Completo</Button>
                    </Link>
                  ) : (
                    <Button
                      disabled
                      title="Seleccione una plantilla personalizada primero"
                    >
                      Ver Ejemplo Completo
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
