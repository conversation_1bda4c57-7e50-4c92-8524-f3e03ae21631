const fs = require('fs');
const path = require('path');

const ROOT_DIR = path.resolve(__dirname, '../../');
const OUTPUT_FILE = path.resolve(ROOT_DIR, 'project_metadata.json');
const EXCLUDE_DIRS = ['node_modules', '.git', '.next', 'out', '.vercel', '.vscode', '.github'];
const INCLUDE_EXTS = ['.js', '.ts', '.tsx', '.jsx', '.md', '.json', '.yml', '.yaml'];

function isCodeFile(file) {
  return INCLUDE_EXTS.includes(path.extname(file));
}

function shouldExclude(dir) {
  return EXCLUDE_DIRS.some(ex => dir.includes(path.sep + ex + path.sep) || dir.endsWith(path.sep + ex));
}

function extractCommentsAndDocs(content, ext) {
  // Simple regex for JS/TS/MD comments and doc blocks
  if (ext === '.md') return { doc: content.slice(0, 1000) };
  const comments = [];
  const docBlocks = content.match(/\/\*\*[\s\S]*?\*\//g) || [];
  const lineComments = content.match(/\/\/.*$/gm) || [];
  if (docBlocks.length) comments.push(...docBlocks);
  if (lineComments.length) comments.push(...lineComments);
  return { comments: comments.join('\n').slice(0, 1000) };
}

function scanDir(dir) {
  let results = [];
  if (shouldExclude(dir)) return results;
  const files = fs.readdirSync(dir);
  for (const file of files) {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    if (stat.isDirectory()) {
      results = results.concat(scanDir(fullPath));
    } else if (isCodeFile(file)) {
      const relPath = path.relative(ROOT_DIR, fullPath);
      const ext = path.extname(file);
      const content = fs.readFileSync(fullPath, 'utf8');
      const { comments, doc } = extractCommentsAndDocs(content, ext);
      results.push({
        path: relPath,
        ext,
        size: stat.size,
        comments: comments || '',
        doc: doc || '',
        firstLines: content.slice(0, 300),
      });
    }
  }
  return results;
}

function main() {
  const metadata = {
    generatedAt: new Date().toISOString(),
    files: scanDir(ROOT_DIR),
  };
  fs.writeFileSync(OUTPUT_FILE, JSON.stringify(metadata, null, 2));
  console.log(`Project metadata written to ${OUTPUT_FILE}`);
}

if (require.main === module) {
  main();
}