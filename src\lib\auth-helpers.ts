import { supabase } from "./supabase";

/**
 * Verifica el estado de la sesión del usuario y devuelve los datos de sesión
 * Esta función está diseñada para ser ejecutada en cliente
 */
export async function checkUserSession() {
  try {
    // Intentar obtener la sesión actual desde Supabase
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.error("[Auth] Error al verificar sesión:", error);
      return null;
    }

    // Si hay sesión, devolver los datos del usuario
    if (data?.session) {
      console.log("[Auth] Sesión activa:", data.session.user.id);
      return data.session;
    }

    console.log("[Auth] No hay sesión activa");
    return null;
  } catch (err) {
    console.error("[Auth] Error inesperado al verificar sesión:", err);
    return null;
  }
}

/**
 * Log de todas las cookies disponibles en el navegador
 * Útil para depuración
 */
export function logBrowserCookies() {
  if (typeof document === "undefined") return;

  console.log("[Auth] Cookies disponibles en navegador:", document.cookie);

  // Buscar específicamente cookies relacionadas con Supabase
  const supabaseCookies = document.cookie
    .split("; ")
    .filter((cookie) => cookie.includes("sb-") || cookie.includes("supabase"))
    .map((cookie) => cookie.trim());

  if (supabaseCookies.length > 0) {
    console.log("[Auth] Cookies de Supabase encontradas:", supabaseCookies);
  } else {
    console.log("[Auth] No se encontraron cookies de Supabase");
  }
}

/**
 * Cierra la sesión actual y limpia todas las cookies
 */
export async function signOut() {
  try {
    console.log("[Auth] Cerrando sesión...");

    // Limpiar cookies antes del signOut para evitar problemas
    document.cookie.split(";").forEach((cookie) => {
      const [name] = cookie.split("=");
      if (name.trim().includes("supabase") || name.trim().includes("sb-")) {
        document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
        console.log(`[Auth] Eliminada cookie: ${name.trim()}`);
      }
    });

    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error("[Auth] Error al cerrar sesión:", error);
      return false;
    }

    console.log("[Auth] Sesión cerrada correctamente");
    return true;
  } catch (err) {
    console.error("[Auth] Error inesperado al cerrar sesión:", err);
    return false;
  }
}

/**
 * Fuerza una recarga completa con redirección
 * Esta función es útil cuando queremos asegurarnos de que
 * toda la aplicación se recargue completamente
 */
export function forceReload(path: string) {
  if (typeof window === "undefined") return;

  console.log(`[Auth] Forzando recarga completa a: ${path}`);
  window.location.href = path;
}

/**
 * Obtiene la información completa del usuario, incluyendo la fecha de última sesión
 * Utiliza la función get_user_info del backend que accede de forma segura a auth.users
 */
export async function getUserInfo(userId?: string) {
  try {
    console.log(
      "[Auth] Obteniendo información de usuario",
      userId ? `para: ${userId}` : "actual",
    );

    // Llamar a la función RPC get_user_info
    const { data, error } = await supabase.rpc(
      "get_user_info",
      userId ? { user_id: userId } : {},
    );

    if (error) {
      console.error("[Auth] Error al obtener información del usuario:", error);
      return null;
    }

    return data && data.length > 0 ? data[0] : null;
  } catch (err) {
    console.error(
      "[Auth] Error inesperado al obtener información del usuario:",
      err,
    );
    return null;
  }
}
