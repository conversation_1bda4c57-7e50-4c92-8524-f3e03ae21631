import { createClient } from "@supabase/supabase-js";
import { NextResponse } from "next/server";

interface CourseContentResult {
  content: unknown[];
  objectives: unknown[];
  legalFramework: unknown[];
}

interface ContentItem {
  id: string;
  content_items: Array<{
    id?: string;
    isNew?: boolean;
    isDeleted?: boolean;
    content_id?: string;
    [key: string]: unknown;
  }>;
  [key: string]: unknown;
}

// Initialize Supabase client with SERVICE_ROLE key for admin actions
let supabaseAdmin: ReturnType<typeof createClient> | null = null;
try {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl) {
    console.error(
      "[API Init] Missing environment variable: NEXT_PUBLIC_SUPABASE_URL",
    );
    throw new Error("Server configuration error: Supabase URL missing.");
  }
  if (!serviceKey) {
    console.error(
      "[API Init] Missing environment variable: SUPABASE_SERVICE_ROLE_KEY",
    );
    throw new Error(
      "Server configuration error: Supabase service role key missing.",
    );
  }

  supabaseAdmin = createClient(supabaseUrl, serviceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
  console.log("[API Init] Supabase admin client initialized successfully.");
} catch (initError) {
  console.error(
    "[API Init] Failed to initialize Supabase admin client:",
    initError,
  );
  // supabaseAdmin remains null if initialization fails
}

// GET handler to fetch course content
export async function GET(request: Request) {
  try {
    if (!supabaseAdmin) {
      console.error(
        "[API GET] Supabase admin client is not available due to initialization error.",
      );
      return NextResponse.json(
        { error: "Server configuration error." },
        { status: 500 },
      );
    }

    // Get courseId from URL
    const url = new URL(request.url);
    const courseId = url.searchParams.get("courseId");
    const section = url.searchParams.get("section"); // Nuevo parámetro para cargar solo una sección específica

    if (!courseId) {
      return NextResponse.json(
        { error: "Course ID is required" },
        { status: 400 },
      );
    }

    // Objeto para almacenar los resultados
    const result: CourseContentResult = {
      content: [],
      objectives: [],
      legalFramework: [],
    };

    // Ejecutar consultas en paralelo para mejorar el rendimiento
    const promises = [];

    // Solo cargar contenido si no se especifica una sección o si se solicita específicamente
    if (!section || section === "content") {
      promises.push(
        (async () => {
          // Fetch course content
          const { data: courseContent, error: contentError } =
            await supabaseAdmin
              .from("course_content")
              .select("*")
              .eq("course_id", courseId);

          if (contentError) {
            console.error(
              "[API GET] Error fetching course content:",
              contentError,
            );
            throw contentError;
          }

          // Fetch content items for each content type
          result.content = await Promise.all(
            (courseContent || []).map(async (content) => {
              const { data: items, error: itemsError } = await supabaseAdmin
                .from("content_items")
                .select("*")
                .eq("content_id", content.id)
                .order("order_num");

              if (itemsError) {
                console.error(
                  "[API GET] Error fetching content items:",
                  itemsError,
                );
                throw itemsError;
              }

              return {
                ...content,
                content_items: items || [],
              };
            }),
          );
        })(),
      );
    }

    // Solo cargar objetivos si no se especifica una sección o si se solicita específicamente
    if (!section || section === "objectives") {
      promises.push(
        (async () => {
          // Fetch course objectives
          const { data: objectives, error: objectivesError } =
            await supabaseAdmin
              .from("course_objectives")
              .select("*")
              .eq("course_id", courseId)
              .order("order_num");

          if (objectivesError) {
            console.error(
              "[API GET] Error fetching course objectives:",
              objectivesError,
            );
            throw objectivesError;
          }

          result.objectives = objectives || [];
        })(),
      );
    }

    // Solo cargar marco legal si no se especifica una sección o si se solicita específicamente
    if (!section || section === "legalFramework") {
      promises.push(
        (async () => {
          // Fetch course legal framework
          const { data: legalFramework, error: legalError } =
            await supabaseAdmin
              .from("course_legal_framework")
              .select("*")
              .eq("course_id", courseId)
              .order("order_num");

          if (legalError) {
            console.error(
              "[API GET] Error fetching course legal framework:",
              legalError,
            );
            throw legalError;
          }

          result.legalFramework = legalFramework || [];
        })(),
      );
    }

    // Esperar a que todas las consultas terminen
    await Promise.all(promises);

    // Configurar caché para mejorar el rendimiento
    const headers = new Headers();
    headers.set(
      "Cache-Control",
      "max-age=60, s-maxage=60, stale-while-revalidate=300",
    );

    return NextResponse.json(result, { headers });
  } catch (error: unknown) {
    console.error("[API GET] Unexpected error:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An unexpected error occurred" },
      { status: 500 },
    );
  }
}

// POST handler to save course content
export async function POST(request: Request) {
  try {
    if (!supabaseAdmin) {
      console.error(
        "[API POST] Supabase admin client is not available due to initialization error.",
      );
      return NextResponse.json(
        { error: "Server configuration error." },
        { status: 500 },
      );
    }

    const { courseId, content, objectives, legalFramework } =
      await request.json();

    if (!courseId) {
      return NextResponse.json(
        { error: "Course ID is required" },
        { status: 400 },
      );
    }
    // Process course content
    if (content && Array.isArray(content)) {
      for (const contentItem of content) {
        // Handle content creation or update
        if (contentItem.isNew) {
          const { data: newContent, error: createContentError } =
            await supabaseAdmin
              .from("course_content")
              .insert({
                course_id: courseId,
                content_type: contentItem.content_type,
                title: contentItem.title,
                description: contentItem.description || null,
              })
              .select()
              .single();

          if (createContentError) throw createContentError;

          // Update content ID for items
          contentItem.id = newContent.id;
        } else {
          // Update existing content
          const { error: updateContentError } = await supabaseAdmin
            .from("course_content")
            .update({
              title: contentItem.title,
              description: contentItem.description || null,
            })
            .eq("id", contentItem.id);

          if (updateContentError) throw updateContentError;
        }

        // Process content items
        if (contentItem.content_items && contentItem.content_items.length > 0) {
          // Items to create (new)
          const itemsToCreate = contentItem.content_items
            .filter((item) => item.isNew && !item.isDeleted)
            .map(({ isNew, isDeleted, ...item }) => ({
              ...item,
              content_id: contentItem.id,
            }));

          if (itemsToCreate.length > 0) {
            const { error: createItemsError } = await supabaseAdmin
              .from("content_items")
              .insert(itemsToCreate);

            if (createItemsError) throw createItemsError;
          }

          // Items to delete
          const itemsToDelete = contentItem.content_items
            .filter((item) => item.isDeleted && !item.isNew)
            .map((item) => item.id);

          if (itemsToDelete.length > 0) {
            const { error: deleteItemsError } = await supabaseAdmin
              .from("content_items")
              .delete()
              .in("id", itemsToDelete);

            if (deleteItemsError) throw deleteItemsError;
          }

          // Items to update
          const itemsToUpdate = contentItem.content_items.filter(
            (item) => !item.isNew && !item.isDeleted,
          );

          for (const item of itemsToUpdate) {
            const { error: updateItemError } = await supabaseAdmin
              .from("content_items")
              .update({
                description: item.description,
                order_num: item.order_num,
              })
              .eq("id", item.id);

            if (updateItemError) throw updateItemError;
          }
        }
      }
    }

    // Process objectives
    if (objectives && Array.isArray(objectives)) {
      // Objectives to create (new)
      const objectivesToCreate = objectives
        .filter((obj) => obj.isNew && !obj.isDeleted)
        .map(({ isNew, isDeleted, ...obj }) => ({
          ...obj,
          course_id: courseId,
        }));

      if (objectivesToCreate.length > 0) {
        const { error: createObjectivesError } = await supabaseAdmin
          .from("course_objectives")
          .insert(objectivesToCreate);

        if (createObjectivesError) throw createObjectivesError;
      }

      // Objectives to delete
      const objectivesToDelete = objectives
        .filter((obj) => obj.isDeleted && !obj.isNew)
        .map((obj) => obj.id);

      if (objectivesToDelete.length > 0) {
        const { error: deleteObjectivesError } = await supabaseAdmin
          .from("course_objectives")
          .delete()
          .in("id", objectivesToDelete);

        if (deleteObjectivesError) throw deleteObjectivesError;
      }

      // Objectives to update
      const objectivesToUpdate = objectives.filter(
        (obj) => !obj.isNew && !obj.isDeleted,
      );

      for (const obj of objectivesToUpdate) {
        const { error: updateObjectiveError } = await supabaseAdmin
          .from("course_objectives")
          .update({
            description: obj.description,
            order_num: obj.order_num,
          })
          .eq("id", obj.id);

        if (updateObjectiveError) throw updateObjectiveError;
      }
    }

    // Process legal framework
    if (legalFramework && Array.isArray(legalFramework)) {
      // Legal framework items to create (new)
      const frameworkToCreate = legalFramework
        .filter((item) => item.isNew && !item.isDeleted)
        .map(({ isNew, isDeleted, ...item }) => ({
          ...item,
          course_id: courseId,
        }));

      if (frameworkToCreate.length > 0) {
        const { error: createFrameworkError } = await supabaseAdmin
          .from("course_legal_framework")
          .insert(frameworkToCreate);

        if (createFrameworkError) throw createFrameworkError;
      }

      // Legal framework items to delete
      const frameworkToDelete = legalFramework
        .filter((item) => item.isDeleted && !item.isNew)
        .map((item) => item.id);

      if (frameworkToDelete.length > 0) {
        const { error: deleteFrameworkError } = await supabaseAdmin
          .from("course_legal_framework")
          .delete()
          .in("id", frameworkToDelete);

        if (deleteFrameworkError) throw deleteFrameworkError;
      }

      // Legal framework items to update
      const frameworkToUpdate = legalFramework.filter(
        (item) => !item.isNew && !item.isDeleted,
      );

      for (const item of frameworkToUpdate) {
        const { error: updateFrameworkError } = await supabaseAdmin
          .from("course_legal_framework")
          .update({
            description: item.description,
            url: item.url || null,
            order_num: item.order_num,
          })
          .eq("id", item.id);

        if (updateFrameworkError) throw updateFrameworkError;
      }
    }

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    console.error("[API POST] Error saving course content:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 },
    );
  }
}
