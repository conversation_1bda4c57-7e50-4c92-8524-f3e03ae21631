name: Automated Backup System

on:
  # Ejecutar diariamente a las 2:00 AM UTC
  schedule:
    - cron: '0 2 * * *'

  # Permitir ejecución manual
  workflow_dispatch:
    inputs:
      backup_type:
        description: 'Tipo de respaldo'
        required: false
        default: 'full'
        type: choice
        options:
          - full
          - config-only
          - docs-only

      retention_days:
        description: 'Días de retención'
        required: false
        default: '30'
        type: string

env:
  NODE_VERSION: '18'
  BACKUP_RETENTION_DAYS: ${{ github.event.inputs.retention_days || '30' }}

jobs:
  automated-backup:
    name: Ejecutar Respaldo Automatizado
    runs-on: ubuntu-latest

    permissions:
      contents: read
      actions: write

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Obtener historial completo

      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: |
          npm ci --only=production
          # Instalar dependencias específicas para respaldo
          npm install --no-save tar-fs archiver

      - name: 🔍 Verify Backup Script
        run: |
          if [ ! -f "scripts/backup/automated-backup.js" ]; then
            echo "❌ Script de respaldo no encontrado"
            exit 1
          fi

          # Verificar permisos de ejecución
          chmod +x scripts/backup/automated-backup.js

      - name: 🗄️ Setup Supabase CLI (Optional)
        continue-on-error: true
        run: |
          # Instalar Supabase CLI para respaldo de esquema
          curl -fsSL https://supabase.com/install.sh | sh
          echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: 📁 Create Backup Directory
        run: |
          mkdir -p backups
          mkdir -p temp-backup

      - name: 🚀 Execute Backup
        id: backup
        run: |
          echo "🚀 Iniciando respaldo automatizado..."

          # Configurar variables de entorno para el respaldo
          export BACKUP_TYPE="${{ github.event.inputs.backup_type || 'full' }}"
          export RETENTION_DAYS="${{ env.BACKUP_RETENTION_DAYS }}"

          # Ejecutar script de respaldo
          node scripts/backup/automated-backup.js

          # Capturar información del respaldo
          BACKUP_FILE=$(ls -t backups/backup-*.tar.gz | head -n1)
          BACKUP_SIZE=$(du -h "$BACKUP_FILE" | cut -f1)
          BACKUP_NAME=$(basename "$BACKUP_FILE")

          echo "backup_file=$BACKUP_FILE" >> $GITHUB_OUTPUT
          echo "backup_size=$BACKUP_SIZE" >> $GITHUB_OUTPUT
          echo "backup_name=$BACKUP_NAME" >> $GITHUB_OUTPUT

          echo "✅ Respaldo completado: $BACKUP_NAME ($BACKUP_SIZE)"

      - name: 🔍 Verify Backup Integrity
        run: |
          BACKUP_FILE="${{ steps.backup.outputs.backup_file }}"

          if [ ! -f "$BACKUP_FILE" ]; then
            echo "❌ Archivo de respaldo no encontrado"
            exit 1
          fi

          # Verificar que el archivo no esté vacío
          if [ ! -s "$BACKUP_FILE" ]; then
            echo "❌ Archivo de respaldo está vacío"
            exit 1
          fi

          # Verificar integridad del archivo tar
          if ! tar -tzf "$BACKUP_FILE" > /dev/null 2>&1; then
            echo "❌ Archivo de respaldo está corrupto"
            exit 1
          fi

          echo "✅ Integridad del respaldo verificada"

      - name: 📊 Generate Backup Report
        id: report
        run: |
          BACKUP_FILE="${{ steps.backup.outputs.backup_file }}"
          BACKUP_SIZE="${{ steps.backup.outputs.backup_size }}"
          BACKUP_NAME="${{ steps.backup.outputs.backup_name }}"

          # Generar reporte detallado
          cat > backup-report.md << EOF
          # 📋 Reporte de Respaldo Automatizado

          **Fecha:** $(date -u '+%Y-%m-%d %H:%M:%S UTC')
          **Workflow:** ${{ github.workflow }}
          **Run ID:** ${{ github.run_id }}

          ## 📊 Información del Respaldo

          - **Archivo:** \`$BACKUP_NAME\`
          - **Tamaño:** $BACKUP_SIZE
          - **Tipo:** ${{ github.event.inputs.backup_type || 'full' }}
          - **Retención:** ${{ env.BACKUP_RETENTION_DAYS }} días

          ## 📁 Contenido Respaldado

          - ✅ Archivos críticos del proyecto
          - ✅ Configuración de entorno (sin secretos)
          - ✅ Esquema de base de datos
          - ✅ Documentación completa
          - ✅ Scripts y configuraciones

          ## 🔍 Verificación

          - ✅ Integridad del archivo verificada
          - ✅ Tamaño del archivo válido
          - ✅ Formato tar.gz correcto

          ---

          *Respaldo generado automáticamente por GitHub Actions*
          EOF

          echo "report_generated=true" >> $GITHUB_OUTPUT

      - name: 📤 Upload Backup Artifact
        uses: actions/upload-artifact@v4
        with:
          name: backup-${{ github.run_id }}
          path: |
            ${{ steps.backup.outputs.backup_file }}
            backup-report.md
          retention-days: ${{ env.BACKUP_RETENTION_DAYS }}
          compression-level: 0  # Ya está comprimido

      - name: 🧹 Cleanup Temporary Files
        if: always()
        run: |
          # Limpiar archivos temporales
          rm -rf temp-backup

          # Mantener solo los últimos 5 respaldos locales
          cd backups
          ls -t backup-*.tar.gz | tail -n +6 | xargs -r rm -f

      - name: 📝 Summary
        run: |
          echo "## 🎉 Respaldo Completado Exitosamente" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- **Archivo:** ${{ steps.backup.outputs.backup_name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Tamaño:** ${{ steps.backup.outputs.backup_size }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Fecha:** $(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "El respaldo ha sido almacenado como artefacto y estará disponible por ${{ env.BACKUP_RETENTION_DAYS }} días." >> $GITHUB_STEP_SUMMARY

  backup-notification:
    name: Notificar Estado del Respaldo
    runs-on: ubuntu-latest
    needs: automated-backup
    if: always()

    steps:
      - name: 📧 Prepare Notification
        id: notification
        run: |
          if [ "${{ needs.automated-backup.result }}" == "success" ]; then
            echo "status=✅ Exitoso" >> $GITHUB_OUTPUT
            echo "color=good" >> $GITHUB_OUTPUT
            echo "message=Respaldo automatizado completado exitosamente" >> $GITHUB_OUTPUT
          else
            echo "status=❌ Fallido" >> $GITHUB_OUTPUT
            echo "color=danger" >> $GITHUB_OUTPUT
            echo "message=Error durante el respaldo automatizado" >> $GITHUB_OUTPUT
          fi

          echo "timestamp=$(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> $GITHUB_OUTPUT

      - name: 📊 Create Status Badge
        run: |
          # Crear badge de estado del respaldo
          STATUS="${{ steps.notification.outputs.status }}"
          TIMESTAMP="${{ steps.notification.outputs.timestamp }}"

          echo "Respaldo: $STATUS - $TIMESTAMP" > backup-status.txt

          echo "## 📊 Estado del Sistema de Respaldo" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Estado:** $STATUS" >> $GITHUB_STEP_SUMMARY
          echo "**Última Ejecución:** $TIMESTAMP" >> $GITHUB_STEP_SUMMARY
          echo "**Workflow:** [${{ github.workflow }}](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY

  # Job opcional para respaldo en almacenamiento externo
  external-backup:
    name: Respaldo Externo (Opcional)
    runs-on: ubuntu-latest
    needs: automated-backup
    if: success() && github.event_name == 'schedule'

    steps:
      - name: 📥 Download Backup Artifact
        uses: actions/download-artifact@v4
        with:
          name: backup-${{ github.run_id }}
          path: ./backup-download

      - name: ☁️ Upload to External Storage
        run: |
          echo "🔄 Configurando respaldo externo..."

          # Aquí se puede configurar la subida a servicios como:
          # - AWS S3
          # - Google Cloud Storage
          # - Azure Blob Storage
          # - Dropbox
          # - etc.

          # Ejemplo para AWS S3 (requiere configurar secrets):
          # aws s3 cp ./backup-download/ s3://your-backup-bucket/qr-curse/ --recursive

          echo "⚠️  Respaldo externo no configurado - implementar según necesidades"
          echo "📁 Archivos disponibles para respaldo externo:"
          ls -la ./backup-download/
