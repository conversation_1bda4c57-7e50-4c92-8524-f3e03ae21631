# 🤝 Contributing to QR CURSE

¡Gracias por tu interés en contribuir a QR CURSE! Este documento proporciona guías y mejores prácticas para contribuir efectivamente al proyecto.

## 📋 Tabla de Contenidos

- [Código de Conducta](#código-de-conducta)
- [Cómo Contribuir](#cómo-contribuir)
- [Proceso de Desarrollo](#proceso-de-desarrollo)
- [Estándares de Código](#estándares-de-código)
- [Testing](#testing)
- [Documentación](#documentación)
- [Pull Requests](#pull-requests)
- [Reportar Issues](#reportar-issues)

## 📜 Código de Conducta

Este proyecto adhiere a un código de conducta. Al participar, se espera que mantengas este código. Por favor reporta comportamientos inaceptables a [<EMAIL>].

### Nuestros Valores

- **Respeto**: Tratamos a todos con respeto y dignidad
- **Inclusión**: Valoramos la diversidad y creamos un ambiente inclusivo
- **Colaboración**: Trabajamos juntos hacia objetivos comunes
- **Excelencia**: Nos esforzamos por la calidad en todo lo que hacemos
- **Transparencia**: Comunicamos abierta y honestamente

## 🚀 Cómo Contribuir

### Tipos de Contribuciones

Valoramos todos los tipos de contribuciones:

- 🐛 **Bug Reports**: Reportar problemas o errores
- 💡 **Feature Requests**: Sugerir nuevas funcionalidades
- 📝 **Documentation**: Mejorar o agregar documentación
- 🧪 **Testing**: Agregar o mejorar tests
- 🔧 **Code**: Implementar features o corregir bugs
- 🎨 **Design**: Mejorar UI/UX
- 🌐 **Translation**: Agregar soporte para idiomas

### Antes de Empezar

1. **Lee la documentación** existente
2. **Revisa issues abiertos** para evitar duplicados
3. **Discute cambios grandes** antes de implementar
4. **Configura tu entorno** siguiendo [Developer Onboarding](docs/onboarding/DEVELOPER_ONBOARDING.md)

## 🔄 Proceso de Desarrollo

### 1. Fork y Clone

```bash
# Fork el repositorio en GitHub
# Luego clona tu fork
git clone https://github.com/tu-usuario/qr-curse.git
cd qr-curse

# Agrega el repositorio original como upstream
git remote add upstream https://github.com/iberi22/scaffolding-curses-nextjs-supabase.git
```

### 2. Crear Rama de Feature

```bash
# Actualiza main
git checkout main
git pull upstream main

# Crea nueva rama
git checkout -b feature/descripcion-corta

# Ejemplos de nombres de rama:
# feature/user-profile-edit
# fix/certificate-validation-bug
# docs/api-documentation-update
# test/user-service-coverage
```

### 3. Desarrollo

1. **Implementa los cambios**
2. **Escribe/actualiza tests**
3. **Actualiza documentación**
4. **Verifica estándares de código**

### 4. Testing Local

```bash
# Ejecutar todos los tests
npm test

# Verificar linting
npm run lint

# Verificar tipos
npm run type-check

# Build de producción
npm run build
```

### 5. Commit y Push

```bash
# Agregar cambios
git add .

# Commit con mensaje descriptivo
git commit -m "feat: add user profile editing functionality

- Add ProfileEditForm component with validation
- Implement updateProfile API endpoint
- Add comprehensive tests for profile update flow
- Update user service documentation

Closes #123"

# Push a tu fork
git push origin feature/descripcion-corta
```

## 📝 Estándares de Código

### Convenciones de Naming

```typescript
// Variables y funciones: camelCase
const userName = 'john_doe';
const getUserProfile = () => {};

// Componentes: PascalCase
const UserProfileCard = () => {};

// Constantes: UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com';

// Archivos: kebab-case
// user-profile-card.tsx
// api-client.ts
```

### TypeScript

```typescript
// ✅ Siempre define tipos explícitos
interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user';
}

// ✅ Usa tipos de retorno explícitos
async function fetchUser(id: string): Promise<User | null> {
  // Implementation
}

// ❌ Evita 'any'
// const data: any = response.data;

// ✅ Usa tipos específicos
const data: User = response.data;
```

### React Components

```tsx
// ✅ Props interface bien definida
interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  onClick?: () => void;
  className?: string;
}

// ✅ Componente funcional con destructuring
export function Button({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  onClick,
  className
}: ButtonProps) {
  return (
    <button
      className={cn(
        'rounded-md font-medium transition-colors',
        variant === 'primary' && 'bg-blue-600 text-white hover:bg-blue-700',
        variant === 'secondary' && 'bg-gray-200 text-gray-900 hover:bg-gray-300',
        size === 'sm' && 'px-3 py-1.5 text-sm',
        size === 'md' && 'px-4 py-2',
        size === 'lg' && 'px-6 py-3 text-lg',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      disabled={disabled}
      onClick={onClick}
    >
      {children}
    </button>
  );
}
```

### Servicios

```typescript
// ✅ Extiende BaseService
export class UserService extends BaseService {
  async createUser(data: CreateUserRequest): Promise<ServiceResponse<User>> {
    // Validación
    const emailValidation = this.validateEmail(data.email);
    if (emailValidation) return emailValidation;

    const nameValidation = this.validateRequired(data.name, 'name');
    if (nameValidation) return nameValidation;

    try {
      // Lógica de negocio
      const user = await this.repositories.users.create(data);
      return this.success(user, 'User created successfully');
    } catch (error) {
      return this.error('CREATE_FAILED', 'Failed to create user', error);
    }
  }
}
```

## 🧪 Testing

### Cobertura Requerida

- **Nuevas funciones**: 100% cobertura
- **Componentes**: Al menos 80% cobertura
- **Servicios**: 100% cobertura de métodos públicos
- **Utilidades**: 100% cobertura

### Tipos de Tests

```typescript
// Unit Tests - Funciones individuales
describe('validateEmail', () => {
  it('should return true for valid email', () => {
    expect(validateEmail('<EMAIL>')).toBe(true);
  });

  it('should return false for invalid email', () => {
    expect(validateEmail('invalid-email')).toBe(false);
  });
});

// Component Tests - Comportamiento de componentes
describe('Button', () => {
  it('should render children correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('should call onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);

    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});

// Integration Tests - Flujos completos
describe('User Registration Flow', () => {
  it('should register user successfully', async () => {
    const userData = {
      name: 'John Doe',
      email: '<EMAIL>',
      password: 'securePassword123'
    };

    const result = await userService.register(userData);

    expect(result.success).toBe(true);
    expect(result.data?.email).toBe(userData.email);
  });
});
```

## 📚 Documentación

### JSDoc para Funciones Públicas

```typescript
/**
 * Validates user input data for registration
 *
 * @param data - User registration data
 * @param data.email - User's email address
 * @param data.password - User's password (min 8 characters)
 * @returns Promise resolving to validation result
 *
 * @example
 * ```typescript
 * const result = await validateUserData({
 *   email: '<EMAIL>',
 *   password: 'securePass123'
 * });
 *
 * if (result.isValid) {
 *   // Proceed with registration
 * }
 * ```
 */
async function validateUserData(data: UserRegistrationData): Promise<ValidationResult> {
  // Implementation
}
```

### README Updates

Si tu cambio afecta la funcionalidad principal:

1. **Actualiza README.md** con nuevas instrucciones
2. **Agrega ejemplos** de uso si es necesario
3. **Actualiza screenshots** si cambias la UI

## 🔄 Pull Requests

### Template de PR

Usa este template para tus PRs:

```markdown
## 📝 Descripción

Breve descripción de los cambios realizados.

## 🎯 Tipo de Cambio

- [ ] Bug fix (cambio que corrige un issue)
- [ ] Nueva feature (cambio que agrega funcionalidad)
- [ ] Breaking change (cambio que rompe compatibilidad)
- [ ] Documentación (cambios solo en documentación)
- [ ] Refactoring (cambios de código sin afectar funcionalidad)

## 🧪 Testing

- [ ] Tests unitarios agregados/actualizados
- [ ] Tests de integración agregados/actualizados
- [ ] Tests manuales realizados
- [ ] Cobertura de tests mantenida/mejorada

## 📋 Checklist

- [ ] Código sigue los estándares del proyecto
- [ ] Self-review realizado
- [ ] Documentación actualizada
- [ ] Tests pasan localmente
- [ ] Lint pasa sin errores
- [ ] Build de producción exitoso

## 🔗 Issues Relacionados

Closes #123
Related to #456

## 📸 Screenshots (si aplica)

[Agregar screenshots de cambios en UI]

## 📝 Notas Adicionales

[Cualquier información adicional para reviewers]
```

### Proceso de Review

1. **Automated Checks**: CI/CD debe pasar
2. **Code Review**: Al menos 1 aprobación requerida
3. **Testing**: Verificar que tests cubren cambios
4. **Documentation**: Verificar documentación actualizada

### Criterios de Aprobación

- ✅ Código sigue estándares establecidos
- ✅ Tests comprehensivos incluidos
- ✅ Documentación actualizada
- ✅ No introduce breaking changes sin justificación
- ✅ Performance no se ve afectada negativamente

## 🐛 Reportar Issues

### Antes de Reportar

1. **Busca issues existentes** para evitar duplicados
2. **Verifica en la última versión** del código
3. **Reproduce el problema** consistentemente

### Template de Bug Report

```markdown
## 🐛 Descripción del Bug

Descripción clara y concisa del problema.

## 🔄 Pasos para Reproducir

1. Ve a '...'
2. Haz click en '...'
3. Scroll hasta '...'
4. Ver error

## ✅ Comportamiento Esperado

Descripción de lo que esperabas que pasara.

## 📸 Screenshots

Si aplica, agrega screenshots del problema.

## 🖥️ Información del Entorno

- OS: [e.g. macOS 12.0]
- Browser: [e.g. Chrome 95.0]
- Node Version: [e.g. 18.17.0]
- Project Version: [e.g. 1.2.3]

## 📝 Contexto Adicional

Cualquier otra información relevante sobre el problema.
```

### Template de Feature Request

```markdown
## 🚀 Feature Request

### 📝 Descripción

Descripción clara de la funcionalidad solicitada.

### 🎯 Problema que Resuelve

¿Qué problema específico resuelve esta feature?

### 💡 Solución Propuesta

Descripción de cómo te gustaría que funcione.

### 🔄 Alternativas Consideradas

¿Qué otras soluciones has considerado?

### 📊 Impacto

- [ ] Usuarios finales
- [ ] Desarrolladores
- [ ] Performance
- [ ] Seguridad
- [ ] Mantenibilidad

### 📝 Contexto Adicional

Cualquier información adicional, mockups, o referencias.
```

## 🏆 Reconocimiento

Todos los contribuidores son reconocidos en:

- **README.md**: Lista de contribuidores
- **CHANGELOG.md**: Créditos en releases
- **GitHub**: Contributor insights
- **Comunicaciones**: Menciones en updates del equipo

## 📞 Contacto

- **GitHub Issues**: Para bugs y feature requests
- **GitHub Discussions**: Para preguntas generales
- **Email**: [<EMAIL>] para temas sensibles
- **Slack/Discord**: #qr-curse-dev para chat diario

---

¡Gracias por contribuir a QR CURSE! Tu participación hace que este proyecto sea mejor para todos. 🎉
