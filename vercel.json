{"version": 2, "buildCommand": "yarn build", "devCommand": "yarn dev", "installCommand": "yarn install", "framework": "nextjs", "outputDirectory": ".next", "git": {"deploymentEnabled": {"main": true, "develop": true, "feature/*": false, "hotfix/*": true, "release/*": true}}, "env": {"NEXT_PUBLIC_SUPABASE_URL": "https://mrgcukvyvivpsacohdqh.supabase.co", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1yZ2N1a3Z5dml2cHNhY29oZHFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE4NTI2MDIsImV4cCI6MjA1NzQyODYwMn0.UHN1JujAjGStmJ37gVRKgRoAwhiJHShzV7RU4cy5t-4", "SUPABASE_URL": "https://mrgcukvyvivpsacohdqh.supabase.co", "SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1yZ2N1a3Z5dml2cHNhY29oZHFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE4NTI2MDIsImV4cCI6MjA1NzQyODYwMn0.UHN1JujAjGStmJ37gVRKgRoAwhiJHShzV7RU4cy5t-4", "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1yZ2N1a3Z5dml2cHNhY29oZHFoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTg1MjYwMiwiZXhwIjoyMDU3NDI4NjAyfQ.CUXI_kUz2ZKjp4471rh8d8aj5q-9grFL0qU4fNlCzOw", "NEXT_DISABLE_ESLINT": "1", "NEXT_DISABLE_TYPESCRIPT": "1", "NODE_OPTIONS": "--max_old_space_size=4096"}, "github": {"silent": true, "autoAlias": true, "enabled": true}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "builds": [{"src": "next.config.js", "use": "@vercel/next"}]}