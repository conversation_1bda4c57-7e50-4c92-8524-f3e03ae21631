const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Use the same configuration as your app
const supabaseUrl = "https://mrgcukvyvivpsacohdqh.supabase.co";
const supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1yZ2N1a3Z5dml2cHNhY29oZHFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE4NTI2MDIsImV4cCI6MjA1NzQyODYwMn0.UHN1JujAjGStmJ37gVRKgRoAwhiJHShzV7RU4cy5t-4";

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function backupDatabase() {
  try {
    console.log('Starting backup using Supabase client...');

    let backupContent = '-- Database Backup via Supabase API\n';
    backupContent += '-- Generated on: ' + new Date().toISOString() + '\n\n';

    // List of tables to backup (based on your schema)
    const tables = [
      'users',
      'certificates',
      'courses',
      'certificate_templates',
      'attendance',
      'course_content',
      'course_instructors',
      'course_objectives',
      'course_legal_frameworks'
    ];

    for (const table of tables) {
      console.log(`Backing up table: ${table}`);

      try {
        const { data, error } = await supabase
          .from(table)
          .select('*');

        if (error) {
          console.log(`Error accessing table ${table}:`, error.message);
          continue;
        }

        if (data && data.length > 0) {
          backupContent += `-- Data for table: ${table}\n`;
          backupContent += `-- Found ${data.length} records\n\n`;

          // Export as JSON for easier handling
          backupContent += `-- JSON data for ${table}:\n`;
          backupContent += `-- ${JSON.stringify(data, null, 2)}\n\n`;

          // Also create INSERT statements
          backupContent += `-- INSERT statements for ${table}:\n`;
          data.forEach(row => {
            const columns = Object.keys(row);
            const values = Object.values(row).map(value => {
              if (value === null) return 'NULL';
              if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
              return value;
            });
            backupContent += `INSERT INTO "${table}" (${columns.map(c => `"${c}"`).join(', ')}) VALUES (${values.join(', ')});\n`;
          });
          backupContent += '\n';
        } else {
          backupContent += `-- Table ${table} is empty\n\n`;
        }

      } catch (tableError) {
        console.log(`Error processing table ${table}:`, tableError.message);
        backupContent += `-- Error accessing table ${table}: ${tableError.message}\n\n`;
      }
    }

    // Write to file
    fs.writeFileSync('backup-supabase.sql', backupContent);
    console.log('Backup completed successfully! File: backup-supabase.sql');

    // Also create a JSON backup
    const jsonBackup = {};
    for (const table of tables) {
      try {
        const { data, error } = await supabase.from(table).select('*');
        if (!error && data) {
          jsonBackup[table] = data;
        }
      } catch (e) {
        console.log(`Error getting JSON for ${table}:`, e.message);
      }
    }

    fs.writeFileSync('backup-supabase.json', JSON.stringify(jsonBackup, null, 2));
    console.log('JSON backup completed! File: backup-supabase.json');

  } catch (error) {
    console.error('Error during backup:', error);
  }
}

backupDatabase();