import { createClient } from "@supabase/supabase-js";
import { NextResponse } from "next/server";

// IMPORTANT: Initialize Supabase client with SERVICE_ROLE key for admin actions
// Ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your environment variables
// DO NOT expose this key client-side
let supabaseAdmin: ReturnType<typeof createClient> | null = null;
try {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl) {
    console.error(
      "[API Init] Missing environment variable: NEXT_PUBLIC_SUPABASE_URL",
    );
    throw new Error("Server configuration error: Supabase URL missing.");
  }
  if (!serviceKey) {
    console.error(
      "[API Init] Missing environment variable: SUPABASE_SERVICE_ROLE_KEY",
    );
    throw new Error(
      "Server configuration error: Supabase service role key missing.",
    );
  }

  supabaseAdmin = createClient(supabaseUrl, serviceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
  console.log("[API Init] Supabase admin client initialized successfully.");
} catch (initError) {
  console.error(
    "[API Init] Failed to initialize Supabase admin client:",
    initError,
  );
  // supabaseAdmin remains null if initialization fails
}

export async function POST(request: Request) {
  try {
    // Check if client was initialized
    if (!supabaseAdmin) {
      console.error(
        "[API POST] Supabase admin client is not available due to initialization error.",
      );
      return NextResponse.json(
        { error: "Server configuration error." },
        { status: 500 },
      );
    }

    const { email, password, firstName, lastName, identityDocument } =
      await request.json();

    // Basic validation
    if (!email || !password || !firstName || !lastName || !identityDocument) {
      return NextResponse.json(
        {
          error:
            "All fields are required: email, password, firstName, lastName, and identityDocument",
        },
        { status: 400 },
      );
    }
    if (password.length < 6) {
      return NextResponse.json(
        { error: "Password must be at least 6 characters long" },
        { status: 400 },
      );
    }

    console.log(`[API] Attempting to create user: ${email}`);

    // Use the admin client to create the user
    console.log(
      `[API] Calling supabaseAdmin.auth.admin.createUser for ${email}`,
    ); // Log before call
    const { data: userData, error: createError } =
      await supabaseAdmin.auth.admin.createUser({
        email: email,
        password: password,
        email_confirm: true, // Optionally auto-confirm email, or set to false to require email verification
        user_metadata: {
          role: "student", // Set the default role for new students
          first_name: firstName,
          last_name: lastName,
          identity_document: identityDocument,
        },
      });

    if (createError) {
      console.error(`[API] Error creating user ${email}:`, createError);
      // Provide more specific error messages based on Supabase error codes if possible
      if (
        createError.message.includes("duplicate key") ||
        createError.message.includes("already registered")
      ) {
        return NextResponse.json(
          { error: "Este correo electrónico ya está registrado." },
          { status: 409 },
        ); // Conflict
      }
      return NextResponse.json(
        { error: `Error creating user: ${createError.message}` },
        { status: 500 },
      );
    }

    console.log(
      `[API] User ${email} created successfully with ID: ${userData.user.id}`,
    );

    // Update the corresponding public.users row that was created by the trigger
    const { error: profileError } = await supabaseAdmin.from("users").upsert(
      {
        id: userData.user.id,
        email: email,
        role: "student",
        first_name: firstName,
        last_name: lastName,
        identity_document: identityDocument,
      },
      {
        onConflict: "id",
        ignoreDuplicates: false, // We want to update if the record exists
      },
    );

    if (profileError) {
      console.error(
        `[API] Error updating user profile for ${email}:`,
        profileError,
      );
      // If we can't update the profile, delete the auth user to avoid orphaned auth users
      await supabaseAdmin.auth.admin.deleteUser(userData.user.id);
      return NextResponse.json(
        { error: `Error updating user profile: ${profileError.message}` },
        { status: 500 },
      );
    }

    return NextResponse.json(
      { success: true, userId: userData.user.id },
      { status: 201 },
    );
  } catch (error: unknown) {
    // Catch block for the main POST handler logic
    console.error("[API POST] Error caught in create-student route:", error);
    // More specific log
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred";
    return NextResponse.json(
      { error: `Server error: ${errorMessage}` },
      { status: 500 },
    );
  }
}
