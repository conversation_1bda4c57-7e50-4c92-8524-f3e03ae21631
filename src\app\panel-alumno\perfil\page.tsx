"use client";

import { useRouter } from "next/navigation"; // Import useRouter
import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";

// import { User as SupabaseUser } from '@supabase/supabase-js'; // Removed unused import alias
// Define a more specific User type for the profile page if needed, or enhance the one in lib/supabase
interface UserProfile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  email: string | null;
  identity_document?: string | null; // Add identity document field
}

import { Loader2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export default function ProfilePage() {
  const router = useRouter(); // Initialize router
  const [user, setUser] = useState<UserProfile | null>(null); // Use UserProfile type
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [identityDocument, setIdentityDocument] = useState(""); // State for identity document

  useEffect(() => {
    async function fetchUserData() {
      try {
        setLoading(true);
        setError(null);
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (!session?.user) {
          throw new Error("Usuario no autenticado.");
        }

        // Fetch user data including the new field
        const { data: userData, error: userError } = await supabase
          .from("users")
          .select("id, first_name, last_name, email, identity_document") // Select the new field
          .eq("id", session.user.id)
          .single();

        if (userError) throw userError;

        if (userData) {
          setUser(userData as UserProfile); // Cast to UserProfile
          setFirstName(userData.first_name || "");
          setLastName(userData.last_name || "");
          setEmail(userData.email || "");
          setIdentityDocument(userData.identity_document || ""); // Set state for identity document
        } else {
          // This is the correct 'else' block
          throw new Error("No se encontraron datos del perfil.");
        }
        // Removed the duplicated lines 62-65 from the previous corrupted state
      } catch (err: unknown) {
        // Use unknown
        if (err instanceof Error) {
          setError(err.message || "Error al cargar el perfil.");
        } else {
          setError("Ocurrió un error desconocido al cargar el perfil.");
        }
        console.error("Error fetching profile:", err);
      } finally {
        setLoading(false);
      }
    }
    fetchUserData();
  }, []);

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null); // Clear previous errors
    setSuccess(null);

    // Basic validation for required fields
    if (!firstName || !lastName || !identityDocument) {
      setError(
        "Todos los campos (Nombres, Apellidos, Documento de Identidad) son obligatorios.",
      );
      return; // Stop submission if validation fails
    }

    if (!user) {
      setError("No se pudo identificar al usuario.");
      return;
    }

    setSaving(true);

    try {
      const updates = {
        id: user.id, // Keep the user ID for the update condition
        first_name: firstName,
        last_name: lastName,
        identity_document: identityDocument, // Include identity document in updates
        updated_at: new Date().toISOString(),
      };

      const { error: updateError } = await supabase
        .from("users")
        .update(updates)
        .eq("id", user.id);

      if (updateError) throw updateError;

      setSuccess("Perfil actualizado correctamente. Redirigiendo...");
      // Update local state including the new field
      setUser((prev) =>
        prev
          ? {
              ...prev,
              first_name: firstName,
              last_name: lastName,
              identity_document: identityDocument,
            }
          : null,
      );

      // Redirect back to dashboard after a short delay
      setTimeout(() => {
        router.push("/panel-alumno");
      }, 1500); // 1.5 second delay
    } catch (err: unknown) {
      // Use unknown
      if (err instanceof Error) {
        setError(err.message || "Error al actualizar el perfil.");
      } else {
        setError("Ocurrió un error desconocido al actualizar el perfil.");
      }
      console.error("Error updating profile:", err);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 max-w-2xl">
      <h1 className="text-2xl font-bold mb-6 text-gray-900">Mi Perfil</h1>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      {success && (
        <Alert
          variant="default"
          className="mb-4 bg-green-50 border-green-300 text-green-800"
        >
          <AlertTitle>Éxito</AlertTitle>
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {user ? (
        <form
          onSubmit={handleUpdateProfile}
          className="space-y-6 bg-white p-6 rounded-lg shadow"
        >
          <div>
            <Label htmlFor="email">Correo Electrónico</Label>
            <Input
              id="email"
              type="email"
              value={email}
              disabled
              className="mt-1 bg-gray-100"
            />
            <p className="mt-1 text-xs text-gray-500">
              El correo electrónico no se puede modificar.
            </p>
          </div>
          <div>
            <Label htmlFor="firstName">Nombres</Label>
            <Input
              id="firstName"
              type="text"
              value={firstName}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setFirstName(e.target.value)
              }
              required
              className="mt-1"
            />
          </div>
          {/* Input for Apellidos (moved down) */}
          <div>
            <Label htmlFor="lastName">Apellidos</Label>
            <Input
              id="lastName"
              type="text"
              value={lastName}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setLastName(e.target.value)
              }
              required
              // required // Removing again due to persistent linter error
              className="mt-1"
            />
          </div>
          {/* Input for Documento Identidad */}
          <div>
            <Label htmlFor="identityDocument">Documento de Identidad</Label>
            <Input
              id="identityDocument"
              type="text"
              value={identityDocument}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setIdentityDocument(e.target.value)
              }
              placeholder="Ingrese su documento"
              className="mt-1"
            />
          </div>
          {/* Removed the duplicated Apellidos input block */}
          <div className="flex justify-end">
            <Button type="submit" disabled={saving}>
              {saving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Guardando...
                </>
              ) : (
                "Guardar Cambios"
              )}
            </Button>
          </div>
        </form>
      ) : (
        !error && (
          <p className="text-gray-600">
            No se pudo cargar la información del perfil.
          </p>
        )
      )}
    </div>
  );
}
