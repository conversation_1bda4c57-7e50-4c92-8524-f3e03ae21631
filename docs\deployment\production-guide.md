# Production Deployment Guide

Comprehensive guide for deploying and maintaining the QR CURSE platform in production environments.

## Table of Contents

- [Deployment Overview](#deployment-overview)
- [Environment Setup](#environment-setup)
- [Vercel Deployment](#vercel-deployment)
- [Database Configuration](#database-configuration)
- [Environment Variables](#environment-variables)
- [Security Configuration](#security-configuration)
- [Performance Optimization](#performance-optimization)
- [Monitoring and Logging](#monitoring-and-logging)
- [Backup and Recovery](#backup-and-recovery)
- [Maintenance Procedures](#maintenance-procedures)

## Deployment Overview

The QR CURSE platform is designed for deployment on modern cloud platforms with the following architecture:

- **Frontend**: Next.js application deployed on Vercel
- **Database**: Supabase (PostgreSQL) with real-time capabilities
- **Authentication**: Supabase Auth with JWT tokens
- **File Storage**: Supabase Storage for certificates and documents
- **CDN**: Vercel Edge Network for global content delivery

### Deployment Strategy

- **Blue-Green Deployment**: Zero-downtime deployments using Vercel's preview deployments
- **Database Migrations**: Automated schema updates through Supabase CLI
- **Environment Promotion**: Staged deployment through dev → staging → production

## Environment Setup

### Prerequisites

1. **Node.js**: Version 18.17 or higher
2. **npm/yarn/pnpm**: Latest stable version
3. **Git**: For version control
4. **Vercel CLI**: For deployment management
5. **Supabase CLI**: For database management

### Installation

```bash
# Install Vercel CLI
npm install -g vercel

# Install Supabase CLI
npm install -g supabase

# Clone repository
git clone https://github.com/your-org/qr-curse.git
cd qr-curse

# Install dependencies
npm install

# Setup environment
cp .env.example .env.local
```

## Vercel Deployment

### Initial Setup

1. **Connect Repository**
   ```bash
   vercel login
   vercel --prod
   ```

2. **Configure Build Settings**
   - Framework Preset: Next.js
   - Build Command: `npm run build`
   - Output Directory: `.next`
   - Install Command: `npm install`

3. **Environment Variables**
   Configure all required environment variables in Vercel dashboard.

### Deployment Configuration

```json
// vercel.json
{
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "framework": "nextjs",
  "regions": ["iad1", "sfo1"],
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "https://qr-curse.com"
        },
        {
          "key": "Access-Control-Allow-Methods",
          "value": "GET, POST, PUT, DELETE, OPTIONS"
        },
        {
          "key": "Access-Control-Allow-Headers",
          "value": "Content-Type, Authorization"
        }
      ]
    }
  ],
  "redirects": [
    {
      "source": "/admin",
      "destination": "/panel-admin",
      "permanent": true
    }
  ]
}
```

### Deployment Commands

```bash
# Deploy to preview
vercel

# Deploy to production
vercel --prod

# Check deployment status
vercel ls

# View logs
vercel logs [deployment-url]
```

## Database Configuration

### Supabase Setup

1. **Create Project**
   - Go to [Supabase Dashboard](https://supabase.com/dashboard)
   - Create new project
   - Note the project URL and anon key

2. **Database Schema**
   ```sql
   -- Run in Supabase SQL Editor

   -- Enable necessary extensions
   CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
   CREATE EXTENSION IF NOT EXISTS "pgcrypto";

   -- Create custom types
   CREATE TYPE user_role AS ENUM ('admin', 'student', 'instructor');
   CREATE TYPE certificate_status AS ENUM ('active', 'revoked', 'expired');

   -- Create tables (see schema.sql for complete schema)
   ```

3. **Row Level Security (RLS)**
   ```sql
   -- Enable RLS on all tables
   ALTER TABLE users ENABLE ROW LEVEL SECURITY;
   ALTER TABLE certificates ENABLE ROW LEVEL SECURITY;
   ALTER TABLE courses ENABLE ROW LEVEL SECURITY;

   -- Create policies
   CREATE POLICY "Users can view own profile" ON users
     FOR SELECT USING (auth.uid() = id);

   CREATE POLICY "Admins can view all users" ON users
     FOR SELECT USING (
       EXISTS (
         SELECT 1 FROM users
         WHERE id = auth.uid() AND role = 'admin'
       )
     );
   ```

4. **Database Migrations**
   ```bash
   # Initialize Supabase locally
   supabase init

   # Link to remote project
   supabase link --project-ref your-project-ref

   # Create migration
   supabase migration new create_initial_schema

   # Apply migrations
   supabase db push
   ```

### Connection Configuration

```typescript
// lib/supabase.ts
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
  db: {
    schema: 'public',
  },
  global: {
    headers: {
      'X-Client-Info': 'qr-curse-web',
    },
  },
});
```

## Environment Variables

### Required Variables

```bash
# .env.production
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Application Configuration
NEXT_PUBLIC_APP_URL=https://qr-curse.com
NEXT_PUBLIC_APP_NAME="QR CURSE"
NEXT_PUBLIC_APP_VERSION=1.0.0

# Security
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=https://qr-curse.com

# Email Configuration (if using custom SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Analytics (optional)
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Error Tracking (optional)
SENTRY_DSN=https://your-sentry-dsn
```

### Environment Validation

```typescript
// lib/env.ts
import { z } from 'zod';

const envSchema = z.object({
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1),
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1),
  NEXT_PUBLIC_APP_URL: z.string().url(),
  NEXTAUTH_SECRET: z.string().min(32),
});

export const env = envSchema.parse(process.env);
```

## Security Configuration

### Content Security Policy

```typescript
// next.config.js
const securityHeaders = [
  {
    key: 'Content-Security-Policy',
    value: `
      default-src 'self';
      script-src 'self' 'unsafe-eval' 'unsafe-inline' *.vercel-analytics.com;
      style-src 'self' 'unsafe-inline';
      img-src 'self' data: blob: *.supabase.co;
      font-src 'self';
      connect-src 'self' *.supabase.co wss://*.supabase.co;
      frame-src 'none';
    `.replace(/\s{2,}/g, ' ').trim()
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'strict-origin-when-cross-origin'
  },
  {
    key: 'Permissions-Policy',
    value: 'camera=(), microphone=(), geolocation=()'
  }
];

module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
    ];
  },
};
```

### Rate Limiting

```typescript
// lib/rate-limit.ts
import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

export const ratelimit = new Ratelimit({
  redis,
  limiter: Ratelimit.slidingWindow(10, '10 s'),
  analytics: true,
});

// Usage in API routes
export async function withRateLimit(request: Request) {
  const ip = request.headers.get('x-forwarded-for') ?? 'anonymous';
  const { success, limit, reset, remaining } = await ratelimit.limit(ip);

  if (!success) {
    throw new Error('Rate limit exceeded');
  }

  return { limit, reset, remaining };
}
```

## Performance Optimization

### Next.js Configuration

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
    serverComponentsExternalPackages: ['@supabase/supabase-js'],
  },
  images: {
    domains: ['your-project.supabase.co'],
    formats: ['image/webp', 'image/avif'],
  },
  compress: true,
  poweredByHeader: false,
  generateEtags: false,
  swcMinify: true,
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
};

module.exports = nextConfig;
```

### Bundle Analysis

```bash
# Install bundle analyzer
npm install --save-dev @next/bundle-analyzer

# Analyze bundle
npm run build
npm run analyze
```

### Caching Strategy

```typescript
// lib/cache.ts
export const cacheConfig = {
  // Static assets
  static: 'public, max-age=31536000, immutable',

  // API responses
  api: 'public, max-age=300, s-maxage=600',

  // User-specific data
  private: 'private, max-age=0, must-revalidate',

  // Public data with revalidation
  public: 'public, max-age=60, s-maxage=300, stale-while-revalidate=86400',
};

// Usage in API routes
export function setCacheHeaders(response: Response, type: keyof typeof cacheConfig) {
  response.headers.set('Cache-Control', cacheConfig[type]);
  return response;
}
```

## Monitoring and Logging

### Error Tracking with Sentry

```typescript
// lib/sentry.ts
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 0.1,
  beforeSend(event) {
    // Filter out sensitive data
    if (event.request?.headers) {
      delete event.request.headers.authorization;
      delete event.request.headers.cookie;
    }
    return event;
  },
});
```

### Analytics Setup

```typescript
// lib/analytics.ts
import { Analytics } from '@vercel/analytics/react';

export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  return (
    <>
      {children}
      <Analytics />
    </>
  );
}
```

### Health Checks

```typescript
// app/api/health/route.ts
import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET() {
  try {
    // Check database connection
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (error) throw error;

    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'operational',
        api: 'operational',
      },
    });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 503 }
    );
  }
}
```

## Backup and Recovery

### Database Backups

```bash
# Automated daily backups (Supabase handles this automatically)
# Manual backup
supabase db dump --file backup-$(date +%Y%m%d).sql

# Restore from backup
supabase db reset --file backup-20240115.sql
```

### Application Backups

```bash
# Backup environment configuration
vercel env pull .env.backup

# Backup deployment configuration
cp vercel.json vercel.json.backup
```

## Maintenance Procedures

### Deployment Checklist

1. **Pre-deployment**
   - [ ] Run all tests: `npm test`
   - [ ] Check build: `npm run build`
   - [ ] Review environment variables
   - [ ] Backup current deployment

2. **Deployment**
   - [ ] Deploy to staging: `vercel --target staging`
   - [ ] Run smoke tests on staging
   - [ ] Deploy to production: `vercel --prod`
   - [ ] Verify production deployment

3. **Post-deployment**
   - [ ] Monitor error rates
   - [ ] Check performance metrics
   - [ ] Verify critical user flows
   - [ ] Update documentation

### Rollback Procedure

```bash
# List recent deployments
vercel ls

# Promote previous deployment
vercel promote [deployment-url] --scope production

# Or rollback via Vercel dashboard
```

### Database Maintenance

```sql
-- Regular maintenance queries
-- Analyze table statistics
ANALYZE;

-- Vacuum tables
VACUUM ANALYZE;

-- Check for unused indexes
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE schemaname = 'public';
```

This production guide ensures a robust, secure, and maintainable deployment of the QR CURSE platform.
