"use client";

import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface Attendance {
  id: string;
  user_id: string;
  course_id: string;
  date: string;
  status: "PRESENT" | "ABSENT" | "LATE";
  comments: string;
  created_at: string;
  courses: {
    id: string;
    name: string;
  };
}

interface AttendanceStats {
  total: number;
  present: number;
  absent: number;
  late: number;
}

export default function StudentAttendance() {
  const [attendance, setAttendance] = useState<Attendance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<AttendanceStats>({
    total: 0,
    present: 0,
    absent: 0,
    late: 0,
  });
  const supabase = createClientComponentClient();

  useEffect(() => {
    fetchAttendance();
  }, [fetchAttendance]);

  async function fetchAttendance() {
    try {
      setLoading(true);
      setError(null);

      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) throw new Error("No se encontró el usuario");

      const { data: attendanceData, error: attendanceError } = await supabase
        .from("attendance")
        .select(`
          *,
          courses:course_id(id, name)
        `)
        .eq("user_id", user.id)
        .order("date", { ascending: false });

      if (attendanceError) throw attendanceError;

      setAttendance(attendanceData || []);
      calculateStats(attendanceData || []);
    } catch (error) {
      setError("Error al cargar la asistencia");
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  }

  function calculateStats(data: Attendance[]) {
    const stats = data.reduce(
      (acc, record) => {
        acc.total++;
        if (record.status === "PRESENT") acc.present++;
        if (record.status === "ABSENT") acc.absent++;
        if (record.status === "LATE") acc.late++;
        return acc;
      },
      { total: 0, present: 0, absent: 0, late: 0 },
    );

    setStats(stats);
  }

  function calculateAttendancePercentage() {
    if (stats.total === 0) return "0%";
    const percentage = ((stats.present + stats.late) / stats.total) * 100;
    return `${percentage.toFixed(1)}%`;
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Mi Asistencia</h1>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="bg-white rounded-lg shadow p-6 mb-8">
        <h2 className="text-lg font-semibold mb-4">Resumen de Asistencia</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-600">Total de Clases</p>
            <p className="text-2xl font-bold">{stats.total}</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <p className="text-sm text-green-600">Presente</p>
            <p className="text-2xl font-bold">{stats.present}</p>
          </div>
          <div className="bg-red-50 p-4 rounded-lg">
            <p className="text-sm text-red-600">Ausente</p>
            <p className="text-2xl font-bold">{stats.absent}</p>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg">
            <p className="text-sm text-yellow-600">Atrasado</p>
            <p className="text-2xl font-bold">{stats.late}</p>
          </div>
        </div>
        <div className="mt-6">
          <p className="text-sm text-gray-600">Porcentaje de Asistencia</p>
          <p className="text-3xl font-bold">
            {calculateAttendancePercentage()}
          </p>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Fecha</TableHead>
              <TableHead>Curso</TableHead>
              <TableHead>Estado</TableHead>
              <TableHead>Comentarios</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {attendance.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-8">
                  No hay registros de asistencia
                </TableCell>
              </TableRow>
            ) : (
              attendance.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>
                    {new Date(record.date).toLocaleDateString()}
                  </TableCell>
                  <TableCell className="font-medium">
                    {record.courses?.name || "No disponible"}
                  </TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded text-sm ${
                        record.status === "PRESENT"
                          ? "bg-green-100 text-green-800"
                          : record.status === "ABSENT"
                            ? "bg-red-100 text-red-800"
                            : "bg-yellow-100 text-yellow-800"
                      }`}
                    >
                      {record.status === "PRESENT" && "Presente"}
                      {record.status === "ABSENT" && "Ausente"}
                      {record.status === "LATE" && "Atrasado"}
                    </span>
                  </TableCell>
                  <TableCell>{record.comments || "-"}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
