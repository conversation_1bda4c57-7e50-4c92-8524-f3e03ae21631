/* Mejoras de accesibilidad para contraste de color */

/* Mejorar contraste de texto en elementos muted */
.text-gray-400 {
  color: #5a6377 !important; /* Más oscuro que el original para mejor contraste */
}

.text-gray-500 {
  color: #424a59 !important; /* Más oscuro que el original para mejor contraste */
}

.text-gray-600 {
  color: #374151 !important; /* Más oscuro para mejor contraste */
}

.text-gray-700 {
  color: #2d3748 !important; /* Asegurar buen contraste */
}

.text-muted-foreground {
  color: #424a59 !important; /* Más oscuro que el original para mejor contraste */
}

/* Mejorar contraste en botones */
.bg-indigo-600 {
  background-color: #4338ca !important; /* Más saturado para mejor contraste */
}

.text-indigo-200 {
  color: #e0e7ff !important; /* Más claro para mejor contraste sobre fondo oscuro */
}

/* Mejorar contraste en botones primarios */
.bg-primary {
  background-color: #0c8bd9 !important; /* Asegurar buen contraste */
}

.text-primary {
  color: #0c8bd9 !important; /* Asegurar buen contraste */
}

.bg-secondary {
  background-color: #6232c5 !important; /* Asegurar buen contraste */
}

.text-secondary {
  color: #6232c5 !important; /* Asegurar buen contraste */
}

/* Mejorar contraste en enlaces */
a {
  text-decoration-thickness: 1px;
  text-underline-offset: 2px;
}

a:focus {
  outline: 3px solid #1da1f2;
  outline-offset: 2px;
}

/* Mejorar contraste en inputs */
input:focus,
select:focus,
textarea:focus {
  outline: 3px solid #1da1f2;
  outline-offset: 2px;
}

/* Mejorar contraste en botones de acción */
button:focus {
  outline: 3px solid #1da1f2;
  outline-offset: 2px;
}

/* Mejorar contraste en estados hover */
.hover\:bg-gray-100:hover {
  background-color: #e5e7eb !important; /* Más oscuro para mejor contraste */
}

.hover\:text-gray-500:hover {
  color: #374151 !important; /* Más oscuro para mejor contraste */
}

/* Mejorar contraste en elementos de navegación */
.text-white {
  color: #ffffff !important;
}

.bg-indigo-700 {
  background-color: #4338ca !important; /* Más saturado para mejor contraste */
}

/* Mejorar contraste en elementos de formulario */
.placeholder-gray-400::placeholder {
  color: #5a6377 !important; /* Más oscuro para mejor contraste */
}

/* Mejorar contraste en tarjetas */
.shadow-sm {
  box-shadow:
    0 1px 2px 0 rgba(0, 0, 0, 0.1),
    0 1px 3px 0 rgba(0, 0, 0, 0.06) !important;
}

.shadow-md {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.15),
    0 2px 4px -1px rgba(0, 0, 0, 0.1) !important;
}

.shadow-lg {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.15),
    0 4px 6px -2px rgba(0, 0, 0, 0.1) !important;
}

/* Mejorar contraste en iconos */
.text-blue-600 {
  color: #1c64f2 !important; /* Más oscuro para mejor contraste */
}

.text-indigo-600 {
  color: #4f46e5 !important; /* Más oscuro para mejor contraste */
}

.text-purple-600 {
  color: #7e3af2 !important; /* Más oscuro para mejor contraste */
}

.text-primary {
  color: #0c8bd9 !important; /* Más oscuro para mejor contraste */
}

/* Mejorar contraste en fondos */
.bg-blue-100 {
  background-color: #dbeafe !important; /* Más oscuro para mejor contraste */
}

.bg-indigo-100 {
  background-color: #e0e7ff !important; /* Más oscuro para mejor contraste */
}

.bg-purple-100 {
  background-color: #ede9fe !important; /* Más oscuro para mejor contraste */
}

/* Mejorar accesibilidad para lectores de pantalla */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Mejorar contraste en botones de acción */
.btn-gradient {
  background: linear-gradient(90deg, #0c8bd9 0%, #6232c5 100%) !important;
  color: #ffffff !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.btn-gradient:hover {
  background: linear-gradient(90deg, #0a77ba 0%, #5429b3 100%) !important;
  box-shadow: 0 4px 12px rgba(12, 139, 217, 0.4) !important;
}

.btn-gradient:focus {
  outline: 3px solid #1da1f2 !important;
  outline-offset: 2px !important;
}
