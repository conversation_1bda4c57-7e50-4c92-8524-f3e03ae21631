"use client";

import { Check, ChevronsUpDown, User, X } from "lucide-react";
import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

export interface UserOption {
  value: string;
  label: string;
  email?: string;
  identity_document?: string;
}

interface UserComboboxProps {
  options: UserOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  emptyMessage?: string;
  className?: string;
  disabled?: boolean;
}

export function UserCombobox({
  options,
  value,
  onChange,
  placeholder = "Seleccionar usuario...",
  emptyMessage = "No se encontraron usuarios.",
  className,
  disabled = false,
}: UserComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");

  // Reference to the button element
  const buttonRef = React.useRef<HTMLButtonElement>(null);

  // Handle keyboard events on the button
  const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
    // If the user presses a letter key, open the dropdown and set the search query
    if (
      !open &&
      e.key.length === 1 &&
      e.key.match(/[a-z0-9]/i) &&
      !e.ctrlKey &&
      !e.altKey &&
      !e.metaKey
    ) {
      setSearchQuery(e.key);
      setOpen(true);
    }
  };

  // Ensure we have valid options
  const validOptions = React.useMemo(() => {
    return Array.isArray(options) ? options : [];
  }, [options]);

  // Filter options based on search query
  const filteredOptions = React.useMemo(() => {
    console.log("UserCombobox filtering options with query:", searchQuery);
    console.log("UserCombobox available options:", validOptions);

    if (!searchQuery) return validOptions;

    const lowerQuery = searchQuery.toLowerCase();
    const filtered = validOptions.filter(
      (option) =>
        option.label.toLowerCase().includes(lowerQuery) ||
        option.email?.toLowerCase().includes(lowerQuery) ||
        option.identity_document?.toLowerCase().includes(lowerQuery),
    );

    console.log("UserCombobox filtered options:", filtered);
    return filtered;
  }, [validOptions, searchQuery]);

  // Debug when component renders
  React.useEffect(() => {
    console.log("UserCombobox rendered with:", {
      optionsCount: options.length,
      value,
      open,
      searchQuery,
    });
  }, [options.length, value, open, searchQuery]);

  // Debug when open state changes
  React.useEffect(() => {
    console.log("UserCombobox open state changed to:", open);
  }, [open]);

  // Handle direct selection of an option
  const handleSelectOption = (selectedOption: UserOption) => {
    console.log("Selecting option:", selectedOption);
    // Force a small delay to ensure the UI updates correctly
    setTimeout(() => {
      onChange(selectedOption.value);
      setOpen(false);
      setSearchQuery("");
    }, 10);
  };

  // Debug selected value
  React.useEffect(() => {
    if (value) {
      const selectedOption = validOptions.find(
        (option) => option.value === value,
      );
      console.log("Currently selected option:", selectedOption);
    }
  }, [value, validOptions]);

  return (
    <Popover
      open={open}
      onOpenChange={(isOpen) => {
        console.log("Popover onOpenChange called with:", isOpen);
        setOpen(isOpen);
      }}
    >
      <PopoverTrigger asChild>
        <Button
          ref={buttonRef}
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between",
            !value && "text-muted-foreground",
            className,
          )}
          disabled={disabled}
          onKeyDown={handleKeyDown}
          onClick={() => {
            console.log(
              "Button clicked, toggling open state from",
              open,
              "to",
              !open,
            );
            setOpen(!open);
          }}
          type="button" // Explicitly set type to button to prevent form submission
        >
          <div className="flex-1 text-left overflow-hidden text-ellipsis whitespace-nowrap">
            {value
              ? validOptions.find((option) => option.value === value)?.label ||
                placeholder
              : placeholder}
          </div>
          <div className="flex items-center">
            {value && (
              <X
                className="mr-1 h-4 w-4 shrink-0 opacity-70 hover:opacity-100 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  onChange("");
                }}
              />
            )}
            <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="p-0 w-[var(--radix-popover-trigger-width)] z-[110]"
        align="start"
        side="bottom"
        sideOffset={8}
        avoidCollisions={false}
      >
        <Command>
          <CommandInput
            placeholder="Buscar por nombre, email o RUT..."
            value={searchQuery}
            onValueChange={setSearchQuery}
            className="h-9"
          />
          <CommandList className="max-h-[300px] overflow-y-auto">
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup>
              {filteredOptions.length > 0 ? (
                filteredOptions.map((option) => (
                  <CommandItem
                    key={option.value}
                    value={option.value}
                    onSelect={() => {
                      console.log("CommandItem direct selection for:", option);
                      handleSelectOption(option);
                    }}
                    className="cursor-pointer hover:bg-accent hover:text-accent-foreground"
                  >
                    <User className="mr-2 h-4 w-4 shrink-0" />
                    <div className="flex flex-col">
                      <span>{option.label}</span>
                      <div className="flex flex-col text-xs text-muted-foreground">
                        {option.email && <span>{option.email}</span>}
                        {option.identity_document && (
                          <span>RUT: {option.identity_document}</span>
                        )}
                      </div>
                    </div>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        value === option.value ? "opacity-100" : "opacity-0",
                      )}
                    />
                  </CommandItem>
                ))
              ) : (
                <div className="py-6 text-center text-sm">{emptyMessage}</div>
              )}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
