/**
 * Database Adapter Types
 *
 * This file defines the abstract interfaces for database operations,
 * decoupling business logic from specific database implementations (Supabase, etc.)
 *
 * Following the Adapter Pattern to enable easy migration between database providers
 * while maintaining clean separation of concerns.
 */

// ============================================================================
// Core Database Types
// ============================================================================

export interface DatabaseError {
  code?: string;
  message: string;
  details?: any;
}

export interface DatabaseResponse<T = any> {
  data: T | null;
  error: DatabaseError | null;
  count?: number;
}

export interface QueryOptions {
  select?: string;
  limit?: number;
  offset?: number;
  orderBy?: { column: string; ascending?: boolean }[];
  filters?: QueryFilter[];
}

export interface QueryFilter {
  column: string;
  operator:
    | "eq"
    | "neq"
    | "gt"
    | "gte"
    | "lt"
    | "lte"
    | "like"
    | "ilike"
    | "in"
    | "is"
    | "not";
  value: any;
}

export interface InsertOptions {
  returning?: string;
  onConflict?: string;
}

export interface UpdateOptions {
  returning?: string;
}

export interface DeleteOptions {
  returning?: string;
}

// ============================================================================
// Domain Entity Types
// ============================================================================

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  rut?: string;
  identity_document?: string;
  phone?: string;
  role: "admin" | "student" | "instructor";
  company_id?: string;
  created_at?: string;
  updated_at?: string;
  is_active?: boolean;
}

export interface Certificate {
  id: string;
  user_id: string;
  course_id: string;
  certificate_number: string;
  issue_date: string;
  expiry_date?: string;
  status: "active" | "revoked" | "expired";
  qr_code?: string;
  attendance_percentage?: number;
  template_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Course {
  id: string;
  title: string;
  description?: string;
  duration_hours?: number;
  instructor_id?: string;
  company_id?: string;
  start_date?: string;
  end_date?: string;
  status: "draft" | "active" | "completed" | "cancelled";
  created_at?: string;
  updated_at?: string;
}

export interface Grade {
  id: string;
  user_id: string;
  assessment_id: string;
  score: number;
  feedback?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Attendance {
  id: string;
  user_id: string;
  lesson_id: string;
  date: string;
  status: "PRESENT" | "ABSENT" | "LATE" | "EXCUSED";
  comments?: string;
  created_at?: string;
  updated_at?: string;
}

// ============================================================================
// Database Adapter Interface
// ============================================================================

export interface DatabaseAdapter {
  // Generic CRUD operations
  select<T = any>(
    table: string,
    options?: QueryOptions,
  ): Promise<DatabaseResponse<T[]>>;

  selectOne<T = any>(
    table: string,
    options?: QueryOptions,
  ): Promise<DatabaseResponse<T>>;

  insert<T = any>(
    table: string,
    data: Partial<T> | Partial<T>[],
    options?: InsertOptions,
  ): Promise<DatabaseResponse<T>>;

  update<T = any>(
    table: string,
    data: Partial<T>,
    filters: QueryFilter[],
    options?: UpdateOptions,
  ): Promise<DatabaseResponse<T>>;

  delete<T = any>(
    table: string,
    filters: QueryFilter[],
    options?: DeleteOptions,
  ): Promise<DatabaseResponse<T>>;

  // RPC/Function calls
  rpc<T = any>(
    functionName: string,
    params?: Record<string, any>,
  ): Promise<DatabaseResponse<T>>;

  // Transaction support
  transaction<T = any>(
    operations: ((adapter: DatabaseAdapter) => Promise<any>)[],
  ): Promise<DatabaseResponse<T>>;
}

// ============================================================================
// Authentication Adapter Interface
// ============================================================================

export interface AuthUser {
  id: string;
  email: string;
  role?: string;
  last_sign_in_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface AuthSession {
  user: AuthUser;
  access_token: string;
  refresh_token?: string;
  expires_at?: number;
}

export interface AuthResponse<T = any> {
  data: T | null;
  error: DatabaseError | null;
}

export interface SignInCredentials {
  email: string;
  password: string;
}

export interface SignUpCredentials {
  email: string;
  password: string;
  options?: {
    data?: Record<string, any>;
  };
}

export interface AuthAdapter {
  // Authentication operations
  signIn(credentials: SignInCredentials): Promise<AuthResponse<AuthSession>>;
  signUp(credentials: SignUpCredentials): Promise<AuthResponse<AuthUser>>;
  signOut(): Promise<AuthResponse<void>>;

  // Session management
  getSession(): Promise<AuthResponse<AuthSession>>;
  refreshSession(): Promise<AuthResponse<AuthSession>>;

  // User management
  getUser(): Promise<AuthResponse<AuthUser>>;
  updateUser(updates: Partial<AuthUser>): Promise<AuthResponse<AuthUser>>;
}

// ============================================================================
// Storage Adapter Interface
// ============================================================================

export interface StorageFile {
  name: string;
  size: number;
  type: string;
  lastModified: number;
}

export interface StorageResponse<T = any> {
  data: T | null;
  error: DatabaseError | null;
}

export interface UploadOptions {
  cacheControl?: string;
  upsert?: boolean;
  contentType?: string;
}

export interface StorageAdapter {
  // File operations
  upload(
    bucket: string,
    path: string,
    file: File | Blob,
    options?: UploadOptions,
  ): Promise<StorageResponse<{ path: string }>>;

  download(bucket: string, path: string): Promise<StorageResponse<Blob>>;

  delete(bucket: string, paths: string[]): Promise<StorageResponse<void>>;

  // URL operations
  getPublicUrl(bucket: string, path: string): string;
  getSignedUrl(
    bucket: string,
    path: string,
    expiresIn: number,
  ): Promise<StorageResponse<string>>;

  // Bucket operations
  createBucket(
    name: string,
    options?: { public?: boolean },
  ): Promise<StorageResponse<void>>;
  listBuckets(): Promise<StorageResponse<string[]>>;
}

// ============================================================================
// Combined Adapter Interface
// ============================================================================

export interface AppAdapter {
  database: DatabaseAdapter;
  auth: AuthAdapter;
  storage: StorageAdapter;
}
