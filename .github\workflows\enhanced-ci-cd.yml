name: Enhanced CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      skip_tests:
        description: 'Skip test execution'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # Job de análisis de cambios
  changes:
    name: Detect Changes
    runs-on: ubuntu-latest
    outputs:
      src: ${{ steps.changes.outputs.src }}
      docs: ${{ steps.changes.outputs.docs }}
      config: ${{ steps.changes.outputs.config }}
      tests: ${{ steps.changes.outputs.tests }}
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4
        
      - name: 🔍 Detect Changes
        uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            src:
              - 'src/**'
              - 'app/**'
            docs:
              - 'docs/**'
              - '*.md'
            config:
              - 'package.json'
              - 'tsconfig.json'
              - 'next.config.js'
              - 'tailwind.config.js'
            tests:
              - '**/*.test.ts'
              - '**/*.test.tsx'
              - 'jest.config.js'

  # Job de validación de código
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    needs: changes
    if: needs.changes.outputs.src == 'true' || needs.changes.outputs.config == 'true'
    
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 Install Dependencies
        run: npm ci
        
      - name: 🔍 Run ESLint
        run: |
          npm run lint -- --format=json --output-file=eslint-report.json || true
          npm run lint
          
      - name: 🎨 Check Prettier
        run: npm run format:check
        
      - name: 🔒 Security Audit
        run: |
          npm audit --audit-level=moderate
          
      - name: 🧪 Type Check
        run: npm run type-check
        
      - name: 📊 Run Code Audit
        run: |
          chmod +x scripts/audit/code-audit.js
          node scripts/audit/code-audit.js
          
      - name: 📤 Upload Audit Reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: audit-reports-${{ github.run_id }}
          path: |
            audit-reports/
            eslint-report.json
          retention-days: 30

  # Job de testing
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    needs: changes
    if: needs.changes.outputs.src == 'true' || needs.changes.outputs.tests == 'true' || github.event.inputs.skip_tests != 'true'
    
    strategy:
      matrix:
        test-type: [unit, integration, e2e]
        
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 Install Dependencies
        run: npm ci
        
      - name: 🧪 Run Unit Tests
        if: matrix.test-type == 'unit'
        run: |
          npm test -- --coverage --watchAll=false --passWithNoTests
          
      - name: 🔗 Run Integration Tests
        if: matrix.test-type == 'integration'
        run: |
          npm test -- --testPathPattern="integration" --watchAll=false
          
      - name: 🌐 Run E2E Tests
        if: matrix.test-type == 'e2e'
        run: |
          echo "E2E tests would run here"
          # npm run test:e2e
          
      - name: 📊 Upload Coverage
        if: matrix.test-type == 'unit'
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # Job de build
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [changes, code-quality]
    if: always() && (needs.code-quality.result == 'success' || needs.code-quality.result == 'skipped')
    
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 Install Dependencies
        run: npm ci
        
      - name: 🏗️ Build Application
        run: |
          npm run build
          
      - name: 📊 Analyze Bundle
        run: |
          npm run analyze || echo "Bundle analysis not available"
          
      - name: 📤 Upload Build Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-${{ github.run_id }}
          path: |
            .next/
            out/
          retention-days: 7

  # Job de generación de contexto AI
  ai-context:
    name: Generate AI Context
    runs-on: ubuntu-latest
    needs: changes
    if: needs.changes.outputs.src == 'true' || needs.changes.outputs.docs == 'true'
    
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 Install Dependencies
        run: npm ci --only=production
        
      - name: 🤖 Generate AI Context
        run: |
          chmod +x scripts/ai-context/context-generator.js
          node scripts/ai-context/context-generator.js
          
      - name: 📤 Upload AI Context
        uses: actions/upload-artifact@v4
        with:
          name: ai-context-${{ github.run_id }}
          path: ai-context/
          retention-days: 90

  # Job de deployment
  deploy:
    name: Deploy to ${{ github.event.inputs.environment || 'staging' }}
    runs-on: ubuntu-latest
    needs: [test, build]
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    environment: ${{ github.event.inputs.environment || 'staging' }}
    
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4
        
      - name: 📥 Download Build Artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-${{ github.run_id }}
          path: ./
          
      - name: 🚀 Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: ${{ github.event.inputs.environment == 'production' && '--prod' || '' }}
          
      - name: 🔍 Health Check
        run: |
          # Esperar a que el deployment esté listo
          sleep 30
          
          # Verificar que la aplicación responde
          DEPLOY_URL="${{ steps.deploy.outputs.preview-url }}"
          if [ -n "$DEPLOY_URL" ]; then
            curl -f "$DEPLOY_URL/api/health" || exit 1
            echo "✅ Health check passed"
          fi

  # Job de notificaciones
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [code-quality, test, build, deploy]
    if: always()
    
    steps:
      - name: 📊 Prepare Notification
        id: notification
        run: |
          # Determinar estado general
          if [ "${{ needs.deploy.result }}" == "success" ]; then
            echo "status=✅ Deployment Successful" >> $GITHUB_OUTPUT
            echo "color=good" >> $GITHUB_OUTPUT
          elif [ "${{ needs.build.result }}" == "success" ]; then
            echo "status=🏗️ Build Successful" >> $GITHUB_OUTPUT
            echo "color=warning" >> $GITHUB_OUTPUT
          else
            echo "status=❌ Pipeline Failed" >> $GITHUB_OUTPUT
            echo "color=danger" >> $GITHUB_OUTPUT
          fi
          
          # Preparar resumen
          echo "summary<<EOF" >> $GITHUB_OUTPUT
          echo "**Branch:** ${{ github.ref_name }}" >> $GITHUB_OUTPUT
          echo "**Commit:** ${{ github.sha }}" >> $GITHUB_OUTPUT
          echo "**Code Quality:** ${{ needs.code-quality.result }}" >> $GITHUB_OUTPUT
          echo "**Tests:** ${{ needs.test.result }}" >> $GITHUB_OUTPUT
          echo "**Build:** ${{ needs.build.result }}" >> $GITHUB_OUTPUT
          echo "**Deploy:** ${{ needs.deploy.result }}" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          
      - name: 📝 Create Summary
        run: |
          echo "## 🚀 CI/CD Pipeline Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "${{ steps.notification.outputs.summary }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Status:** ${{ steps.notification.outputs.status }}" >> $GITHUB_STEP_SUMMARY
          echo "**Workflow:** [${{ github.workflow }}](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY

  # Job de limpieza
  cleanup:
    name: Cleanup
    runs-on: ubuntu-latest
    needs: [notify]
    if: always()
    
    steps:
      - name: 🧹 Cleanup Old Artifacts
        uses: actions/github-script@v7
        with:
          script: |
            const artifacts = await github.rest.actions.listWorkflowRunArtifacts({
              owner: context.repo.owner,
              repo: context.repo.repo,
              run_id: context.runId,
            });
            
            // Mantener solo los artefactos más recientes
            const oldArtifacts = artifacts.data.artifacts.filter(artifact => {
              const ageInDays = (Date.now() - new Date(artifact.created_at)) / (1000 * 60 * 60 * 24);
              return ageInDays > 7; // Más de 7 días
            });
            
            for (const artifact of oldArtifacts) {
              try {
                await github.rest.actions.deleteArtifact({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  artifact_id: artifact.id,
                });
                console.log(`Deleted artifact: ${artifact.name}`);
              } catch (error) {
                console.log(`Failed to delete artifact ${artifact.name}: ${error.message}`);
              }
            }

# Job condicional para releases
  release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: [deploy]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push' && contains(github.event.head_commit.message, '[release]')
    
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: 🏷️ Generate Tag
        id: tag
        run: |
          # Generar tag basado en package.json
          VERSION=$(node -p "require('./package.json').version")
          echo "version=v$VERSION" >> $GITHUB_OUTPUT
          echo "Generated tag: v$VERSION"
          
      - name: 📝 Generate Changelog
        id: changelog
        run: |
          # Generar changelog desde el último tag
          LAST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
          if [ -n "$LAST_TAG" ]; then
            CHANGELOG=$(git log $LAST_TAG..HEAD --pretty=format:"- %s" --no-merges)
          else
            CHANGELOG=$(git log --pretty=format:"- %s" --no-merges -10)
          fi
          
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGELOG" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          
      - name: 🚀 Create Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ steps.tag.outputs.version }}
          release_name: Release ${{ steps.tag.outputs.version }}
          body: |
            ## 🎉 Release ${{ steps.tag.outputs.version }}
            
            ### Changes
            ${{ steps.changelog.outputs.changelog }}
            
            ### Deployment
            - **Environment:** Production
            - **Commit:** ${{ github.sha }}
            - **Date:** ${{ github.event.head_commit.timestamp }}
          draft: false
          prerelease: false
