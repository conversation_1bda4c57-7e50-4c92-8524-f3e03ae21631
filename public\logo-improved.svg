<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1DA1F2" />
      <stop offset="100%" stop-color="#7B3FE4" />
    </linearGradient>
  </defs>
  
  <!-- Logo Symbol -->
  <g transform="translate(60, 30) scale(0.8)">
    <!-- Small squares -->
    <rect x="35" y="35" width="25" height="25" fill="#1DA1F2" />
    <rect x="80" y="20" width="15" height="15" fill="#1DA1F2" />
    <rect x="110" y="60" width="40" height="40" fill="#1DA1F2" />
    <rect x="190" y="30" width="50" height="50" fill="#1DA1F2" />
    <rect x="110" y="150" width="60" height="60" fill="url(#gradient)" />
    <rect x="190" y="150" width="100" height="100" fill="url(#gradient)" />
    
    <!-- D shape -->
    <path d="M190 80 
             C190 80, 190 80, 190 80
             C190 80, 350 80, 350 80
             C350 80, 350 80, 350 80
             C350 80, 350 180, 350 180
             C350 180, 350 280, 350 280
             C350 280, 350 380, 350 380
             C350 380, 350 380, 350 380
             C350 380, 250 380, 250 380
             C250 380, 150 380, 150 380
             C150 380, 150 380, 150 380
             C150 380, 150 330, 150 330
             C150 330, 150 330, 150 330
             C150 330, 190 330, 190 330
             C190 330, 230 330, 230 330
             C230 330, 270 330, 270 330
             C270 330, 270 330, 270 330
             C270 330, 270 280, 270 280
             C270 280, 270 230, 270 230
             C270 230, 270 180, 270 180
             C270 180, 270 130, 270 130
             C270 130, 270 130, 270 130
             C270 130, 230 130, 230 130
             C230 130, 230 130, 230 130
             C230 130, 190 130, 190 130
             C190 130, 190 130, 190 130
             C190 130, 190 80, 190 80Z" 
          fill="url(#gradient)" />
  </g>
  
  <!-- Text DOMUS -->
  <g transform="translate(140, 400)">
    <path d="M15.5,0H31c8.3,0,15,6.7,15,15v0c0,8.3-6.7,15-15,15H15.5c-8.3,0-15-6.7-15-15v0C0.5,6.7,7.2,0,15.5,0z M15.5,7
      c-4.4,0-8,3.6-8,8v0c0,4.4,3.6,8,8,8H31c4.4,0,8-3.6,8-8v0c0-4.4-3.6-8-8-8H15.5z" fill="#1A202C"/>
    <path d="M66.5,0c8.3,0,15,6.7,15,15v15h-30V15C51.5,6.7,58.2,0,66.5,0z M66.5,7c-4.4,0-8,3.6-8,8v8h16v-8
      C74.5,10.6,70.9,7,66.5,7z" fill="#1A202C"/>
    <path d="M89.5,0h7v23.2l15-23.2h7l-15,23.2l15,6.8h-8.8l-13.2-6.1V30h-7V0z" fill="#1A202C"/>
    <path d="M127.5,0h7v12h15V0h7v30h-7V19h-15v11h-7V0z" fill="#1A202C"/>
    <path d="M181.5,0c8.3,0,15,6.7,15,15v0c0,8.3-6.7,15-15,15h-15V0H181.5z M181.5,7h-8v16h8c4.4,0,8-3.6,8-8v0
      C189.5,10.6,185.9,7,181.5,7z" fill="#1A202C"/>
  </g>
  
  <!-- Text Otec -->
  <g transform="translate(200, 440)">
    <path d="M15.5,0c8.3,0,15,6.7,15,15v0c0,8.3-6.7,15-15,15h-15V0H15.5z M15.5,5h-10v20h10c5.5,0,10-4.5,10-10v0
      C25.5,9.5,21,5,15.5,5z" fill="#7B3FE4"/>
    <path d="M35.5,5h5v10h-5V5z" fill="#7B3FE4"/>
    <path d="M45.5,0h5v25c0,2.8,2.2,5,5,5h5v-5h-5V0h5v5h5v20c0,2.8-2.2,5-5,5h-10c-2.8,0-5-2.2-5-5V0z" fill="#7B3FE4"/>
    <path d="M75.5,0c8.3,0,15,6.7,15,15v0c0,8.3-6.7,15-15,15h-10c-2.8,0-5-2.2-5-5V5c0-2.8,2.2-5,5-5H75.5z M75.5,5h-10v20h10
      c5.5,0,10-4.5,10-10v0C85.5,9.5,81,5,75.5,5z" fill="#7B3FE4"/>
  </g>
</svg>
