"use client";

import { useRouter, useSearchParams } from "next/navigation";
import QRCode from "qrcode";
import { type FormEvent, useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { BasicUserSelect } from "@/components/ui/basic-user-select";
import { Button } from "@/components/ui/button";
import QRCodeGenerator from "@/components/ui/qr-code-generator";
import { Switch } from "@/components/ui/switch";
import { Tooltip } from "@/components/ui/tooltip";
import { supabase } from "@/lib/supabase";

// Define types
type Student = {
  id: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  identity_document?: string; // Added identity_document field
};

type Course = {
  id: string;
  title: string; // Corregido para coincidir con la consulta
  code: string; // Corregido para coincidir con la consulta
};

export default function NewCertificate() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const studentIdParam = searchParams.get("studentId");

  // Form state
  const [studentId, setStudentId] = useState("");
  const [courseId, setCourseId] = useState("");
  const [certificateNumber, setCertificateNumber] = useState("");
  const [issueDate, setIssueDate] = useState(
    new Date().toISOString().split("T")[0],
  );
  const [expiryDate, setExpiryDate] = useState("");
  const [autoExpiry, setAutoExpiry] = useState(true); // Por defecto, activar caducidad automática
  const [status, setStatus] = useState("ACTIVE");
  const [attendancePercentage, setAttendancePercentage] = useState("100"); // Valor predeterminado de 100%

  // Data for dropdowns
  const [students, setStudents] = useState<Student[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);

  // UI states
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [_qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [generatedCertificateId, setGeneratedCertificateId] = useState<
    string | null
  >(null);

  // Load students and courses on component mount
  useEffect(() => {
    async function fetchData() {
      try {
        // Fetch students
        const { data: studentsData, error: studentsError } = await supabase
          .from("users")
          .select("id, email, first_name, last_name, identity_document") // Added identity_document
          .eq("role", "student");

        if (studentsError) {
          console.error("Error fetching students:", studentsError);
          throw studentsError;
        }

        console.log("Fetched students:", studentsData);
        setStudents(studentsData || []);

        // If studentId is provided in URL params, set it
        if (studentIdParam) {
          setStudentId(studentIdParam);
        }

        // Fetch courses
        const { data: coursesData, error: coursesError } = await supabase
          .from("courses")
          .select("id, title, code")
          .order("title", { ascending: true });

        if (coursesError) {
          console.error("Error fetching courses:", coursesError);
          throw coursesError;
        }

        console.log("Fetched courses:", coursesData);
        setCourses(coursesData || []);
      } catch (error: any) {
        console.error("Error fetching data:", error);
        setError(
          "No se pudieron cargar los datos. Por favor, intenta nuevamente.",
        );
      } finally {
        setLoadingData(false);
      }
    }

    fetchData();
  }, [studentIdParam]);

  // Generate certificate number on component mount
  useEffect(() => {
    // Generate a certificate number format: CERT-YYYYMM-XXXX
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const random = Math.floor(1000 + Math.random() * 9000); // 4-digit random number
    setCertificateNumber(`CERT-${year}${month}-${random}`);
  }, []);

  // Estado para almacenar la duración de la caducidad en años
  const [expiryYears, setExpiryYears] = useState(1); // Por defecto, 1 año

  // Función de utilidad para calcular la fecha de vencimiento
  const calculateExpiryDate = (years: number, baseDate: string) => {
    const baseDateTime = new Date(baseDate);
    const expiryDateTime = new Date(baseDateTime);
    expiryDateTime.setFullYear(expiryDateTime.getFullYear() + years);
    return expiryDateTime.toISOString().split("T")[0];
  };

  // Calculate expiry date when issue date changes (if auto expiry is enabled)
  useEffect(() => {
    if (autoExpiry && issueDate) {
      // Usar la función de utilidad para calcular la fecha
      setExpiryDate(calculateExpiryDate(expiryYears, issueDate));
    }
  }, [issueDate, autoExpiry, expiryYears, calculateExpiryDate]);

  // Generate QR Curse for the certificate
  const generateQRCode = async (certificateId: string) => {
    try {
      // Create the verification URL with a fixed domain instead of window.location.origin
      // This prevents issues with Vercel preview URLs
      const verificationUrl = `/verificar-certificado/certificado/${certificateId}`;

      // Usar el método directo de QRCode para generar el QR
      // Si falla, capturar el error y devolver una imagen de placeholder
      try {
        // Verificar que QRCode esté disponible
        if (typeof QRCode === "undefined" || !QRCode.toDataURL) {
          throw new Error("QRCode library not available");
        }

        const qrCodeData = await QRCode.toDataURL(verificationUrl, {
          errorCorrectionLevel: "H",
          margin: 1,
          width: 300,
        });
        return qrCodeData;
      } catch (qrError) {
        console.error("Error in QRCode.toDataURL:", qrError);
        // Retornar una URL de imagen de QR de placeholder en caso de error
        return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
      }
    } catch (error) {
      console.error("Error general generating QR Curse:", error);
      // Retornar una URL de imagen de QR de placeholder en caso de error
      return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
    }
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validate form
      if (
        !studentId ||
        !courseId ||
        !certificateNumber ||
        !issueDate ||
        !status ||
        !attendancePercentage
      ) {
        throw new Error("Por favor, completa todos los campos obligatorios.");
      }

      // Validate attendance percentage is a number between 0 and 100
      const attendanceValue = parseFloat(attendancePercentage);
      if (
        Number.isNaN(attendanceValue) ||
        attendanceValue < 0 ||
        attendanceValue > 100
      ) {
        throw new Error(
          "El porcentaje de asistencia debe ser un número entre 0 y 100.",
        );
      }

      // Generate UUID for certificate
      const certificateId = uuidv4();

      // Generate QR Curse
      const qrCodeData = await generateQRCode(certificateId);
      setQrCodeUrl(qrCodeData);

      // Create certificate in database
      const { error: insertError } = await supabase
        .from("certificates")
        .insert([
          {
            id: certificateId,
            user_id: studentId,
            course_id: courseId,
            certificate_number: certificateNumber,
            issue_date: issueDate,
            expiry_date: expiryDate || null,
            status,
            qr_code: qrCodeData,
            attendance_percentage: parseFloat(attendancePercentage), // Incluir el porcentaje de asistencia
          },
        ]);

      if (insertError) throw insertError;

      setSuccess(true);
      setGeneratedCertificateId(certificateId);

      // Redirect after a delay
      setTimeout(() => {
        router.push(`/panel-admin/certificados/${certificateId}`);
      }, 3000);
    } catch (error: any) {
      console.error("Error creating certificate:", error);
      setError(
        error.message ||
          "Error al crear el certificado. Por favor, intenta nuevamente.",
      );
    } finally {
      setLoading(false);
    }
  };

  if (loadingData) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-gray-500">Cargando datos...</p>
      </div>
    );
  }

  // Debug information
  console.log("Current students state:", students);
  console.log("Current studentId:", studentId);
  console.log(
    "UserCombobox options:",
    students.map((student) => ({
      value: student.id,
      label:
        `${student.first_name || ""} ${student.last_name || ""}`.trim() ||
        student.email,
      email: student.email,
      identity_document: student.identity_document,
    })),
  );

  // Handle student selection with improved debugging
  const handleStudentChange = (id: string) => {
    console.log("Student selection changed to ID:", id);
    // Find the selected student for better debugging
    if (id) {
      const selectedStudent = students.find((student) => student.id === id);
      console.log("Selected student:", selectedStudent);
    }
    setStudentId(id);
  };

  return (
    <div>
      <div className="md:grid md:grid-cols-3 md:gap-6">
        <div className="md:col-span-1">
          <div className="px-4 sm:px-0">
            <h3 className="text-lg font-medium leading-6 text-gray-900">
              Emitir Nuevo Certificado
            </h3>
            <p className="mt-1 text-sm text-gray-600">
              Completa el formulario para generar un nuevo certificado con
              código QR para un alumno.
            </p>
            <div className="mt-5">
              <p className="text-sm text-gray-500">
                El certificado se generará automáticamente con un QR que
                permitirá verificar su autenticidad.
              </p>
              <p className="mt-3 text-sm text-gray-500">
                Los campos marcados con * son obligatorios.
              </p>
            </div>
          </div>
        </div>
        <div className="mt-5 md:mt-0 md:col-span-2">
          {success ? (
            <div className="shadow sm:rounded-md sm:overflow-hidden">
              <div className="px-4 py-5 bg-white space-y-6 sm:p-6">
                <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 text-green-400"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-green-800">
                        Certificado Creado Exitosamente
                      </h3>
                      <div className="mt-2 text-sm text-green-700">
                        <p>
                          El certificado ha sido generado y almacenado
                          correctamente.
                        </p>
                        <p className="mt-2">
                          Serás redirigido a la página de detalles en unos
                          segundos...
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-center">
                  <h4 className="text-lg font-medium text-gray-900 mb-4">
                    Código QR generado
                  </h4>
                  <div className="flex justify-center mb-4">
                    {generatedCertificateId ? (
                      <div className="border border-gray-200 p-2 rounded">
                        <QRCodeGenerator
                          url={`/verificar-certificado/certificado/${generatedCertificateId}`}
                          size={200}
                          onError={(error) =>
                            console.error("Error en QRCodeGenerator:", error)
                          }
                        />
                      </div>
                    ) : (
                      <div className="w-48 h-48 flex items-center justify-center bg-gray-100 text-gray-400 text-sm border border-gray-200 p-2 rounded">
                        QR no disponible
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-gray-500 mb-4">
                    Este código QR dirige a la página de verificación del
                    certificado.
                  </p>
                  {generatedCertificateId && (
                    <a
                      href={`/verificar-certificado/certificado/${generatedCertificateId}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-indigo-600 hover:text-indigo-500"
                    >
                      Ver página de verificación
                    </a>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit}>
              <div className="shadow sm:rounded-md sm:overflow-hidden">
                <div className="px-4 py-5 bg-white space-y-6 sm:p-6">
                  {error && (
                    <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  )}

                  {/* Student Select with Basic Select */}
                  <div>
                    <label
                      htmlFor="student"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Alumno *
                    </label>
                    <BasicUserSelect
                      options={students.map((student) => {
                        const firstName = student.first_name || "";
                        const lastName = student.last_name || "";
                        const displayName =
                          `${firstName} ${lastName}`.trim() || student.email;

                        return {
                          value: student.id,
                          label: displayName,
                          email: student.email,
                          identity_document: student.identity_document,
                        };
                      })}
                      value={studentId}
                      onChange={handleStudentChange}
                      placeholder="Seleccionar alumno..."
                    />
                  </div>

                  {/* Course Select */}
                  <div>
                    <label
                      htmlFor="course"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Curso *
                    </label>
                    <select
                      id="course"
                      name="course"
                      value={courseId}
                      onChange={(e) => setCourseId(e.target.value)}
                      className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      required
                    >
                      <option value="">Seleccionar curso</option>
                      {courses.map((course) => (
                        <option key={course.id} value={course.id}>
                          {course.title} ({course.code}){" "}
                          {/* Ya usa title y code, no necesita cambio aquí */}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Certificate Number */}
                  <div>
                    <label
                      htmlFor="certificateNumber"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Número de Certificado *
                    </label>
                    <input
                      type="text"
                      name="certificateNumber"
                      id="certificateNumber"
                      value={certificateNumber}
                      onChange={(e) => setCertificateNumber(e.target.value)}
                      className="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      required
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Este número es generado automáticamente, pero puedes
                      modificarlo si es necesario.
                    </p>
                  </div>

                  {/* Issue Date */}
                  <div>
                    <label
                      htmlFor="issueDate"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Fecha de Emisión *
                    </label>
                    <input
                      type="date"
                      name="issueDate"
                      id="issueDate"
                      value={issueDate}
                      onChange={(e) => setIssueDate(e.target.value)}
                      className="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      required
                    />
                  </div>

                  {/* Expiry Date (Optional) */}
                  <div>
                    <div className="flex justify-between items-center">
                      <label
                        htmlFor="expiryDate"
                        className="block text-sm font-medium text-gray-700"
                      >
                        Fecha de Vencimiento (Opcional)
                      </label>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">
                          {autoExpiry ? "Caducidad automática" : "Personalizar"}
                        </span>
                        <Switch
                          checked={autoExpiry}
                          onCheckedChange={(checked) => {
                            setAutoExpiry(checked);
                            if (checked && issueDate) {
                              // Usar la función de utilidad para calcular la fecha
                              setExpiryDate(
                                calculateExpiryDate(expiryYears, issueDate),
                              );
                            }
                          }}
                        />
                      </div>
                    </div>
                    <div className="mt-1 space-y-2">
                      <input
                        type="date"
                        name="expiryDate"
                        id="expiryDate"
                        value={expiryDate}
                        onChange={(e) => {
                          setExpiryDate(e.target.value);
                          // If manually changing the date, disable auto expiry
                          if (autoExpiry) setAutoExpiry(false);
                        }}
                        className="w-full focus:ring-indigo-500 focus:border-indigo-500 block shadow-sm sm:text-sm border-gray-300 rounded-md"
                      />

                      <div className="flex justify-between gap-2">
                        <Tooltip content="Establece la fecha de vencimiento a 1 año desde la fecha de emisión">
                          <Button
                            type="button"
                            variant={
                              expiryYears === 1 && autoExpiry
                                ? "default"
                                : "outline"
                            }
                            size="sm"
                            className="flex-1"
                            onClick={() => {
                              setExpiryYears(1);
                              setAutoExpiry(true);
                              if (issueDate) {
                                // Usar la función de utilidad para calcular la fecha
                                setExpiryDate(
                                  calculateExpiryDate(1, issueDate),
                                );
                              }
                            }}
                          >
                            1 año
                          </Button>
                        </Tooltip>

                        <Tooltip content="Establece la fecha de vencimiento a 2 años desde la fecha de emisión">
                          <Button
                            type="button"
                            variant={
                              expiryYears === 2 && autoExpiry
                                ? "default"
                                : "outline"
                            }
                            size="sm"
                            className="flex-1"
                            onClick={() => {
                              setExpiryYears(2);
                              setAutoExpiry(true);
                              if (issueDate) {
                                // Usar la función de utilidad para calcular la fecha
                                setExpiryDate(
                                  calculateExpiryDate(2, issueDate),
                                );
                              }
                            }}
                          >
                            2 años
                          </Button>
                        </Tooltip>

                        <Tooltip content="Establece la fecha de vencimiento a 3 años desde la fecha de emisión">
                          <Button
                            type="button"
                            variant={
                              expiryYears === 3 && autoExpiry
                                ? "default"
                                : "outline"
                            }
                            size="sm"
                            className="flex-1"
                            onClick={() => {
                              setExpiryYears(3);
                              setAutoExpiry(true);
                              if (issueDate) {
                                // Usar la función de utilidad para calcular la fecha
                                setExpiryDate(
                                  calculateExpiryDate(3, issueDate),
                                );
                              }
                            }}
                          >
                            3 años
                          </Button>
                        </Tooltip>
                      </div>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      {autoExpiry
                        ? `La fecha de caducidad se actualizará automáticamente a ${expiryYears} ${expiryYears === 1 ? "año" : "años"} desde la fecha de emisión.`
                        : "Deja en blanco si el certificado no tiene fecha de vencimiento."}
                    </p>
                  </div>

                  {/* Status */}
                  <div>
                    <label
                      htmlFor="status"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Estado *
                    </label>
                    <select
                      id="status"
                      name="status"
                      value={status}
                      onChange={(e) => setStatus(e.target.value)}
                      className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      required
                    >
                      <option value="ACTIVE">Activo</option>
                      <option value="REVOKED">Revocado</option>
                      <option value="EXPIRED">Expirado</option>
                    </select>
                  </div>

                  {/* Attendance Percentage */}
                  <div>
                    <label
                      htmlFor="attendancePercentage"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Porcentaje de Asistencia *
                    </label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                      <input
                        type="number"
                        name="attendancePercentage"
                        id="attendancePercentage"
                        value={attendancePercentage}
                        onChange={(e) =>
                          setAttendancePercentage(e.target.value)
                        }
                        min="0"
                        max="100"
                        step="1"
                        className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pr-12 sm:text-sm border-gray-300 rounded-md"
                        required
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 sm:text-sm">%</span>
                      </div>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Porcentaje de asistencia del alumno al curso (0-100%).
                    </p>
                  </div>
                </div>
                <div className="px-4 py-3 bg-gray-50 text-right sm:px-6">
                  <button
                    type="button"
                    onClick={() => router.push("/panel-admin/certificados")}
                    className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 mr-3 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                  >
                    {loading ? "Generando..." : "Emitir Certificado"}
                  </button>
                </div>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
