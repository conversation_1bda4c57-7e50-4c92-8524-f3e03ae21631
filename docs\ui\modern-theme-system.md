# Sistema de Temas Moderno QR CURSE (2025 UI/UX)

## 📋 Descripción General

El sistema de temas moderno de QR CURSE implementa las últimas tendencias de UI/UX 2025, incluyendo:

- **Modo oscuro optimizado** con contraste WCAG 2.1 AA
- **Glassmorphism** y efectos de cristal
- **Neumorphism** para elementos táctiles
- **Micro-interacciones** fluidas
- **Animaciones contextuales**
- **Diseño responsive** y accesible

## 🎨 Paleta de Colores

### Modo Claro
```css
--background: 0 0% 100%;
--foreground: 222.2 84% 4.9%;
--primary: 210 100% 20%;
--secondary: 38 92% 58%;
```

### <PERSON><PERSON>scuro (Optimizado)
```css
--background: 224 71% 4%;
--foreground: 213 31% 91%;
--primary: 217 91% 60%;
--secondary: 47 96% 53%;
```

## 🧩 Componentes Principales

### ModernCard

Tarjeta moderna con efectos glassmorphism y micro-interacciones.

```tsx
import { ModernCard, ModernCardHeader, ModernCardTitle, ModernCardContent } from '@/components/ui/modern-card';

// Tarjeta básica
<ModernCard variant="glass" interactive glowEffect>
  <ModernCardHeader>
    <ModernCardTitle>Título de la Tarjeta</ModernCardTitle>
  </ModernCardHeader>
  <ModernCardContent>
    Contenido de la tarjeta con efectos modernos
  </ModernCardContent>
</ModernCard>

// Tarjeta de estadística
<StatCard
  title="Usuarios Activos"
  value="1,234"
  description="En los últimos 30 días"
  icon={Users}
  trend="up"
  trendValue="+12%"
  variant="glass"
/>
```

**Variantes disponibles:**
- `default` - Estilo estándar
- `glass` - Efecto glassmorphism
- `neuro` - Efecto neumorphism
- `elevated` - Sombra elevada
- `minimal` - Diseño minimalista

### ModernButton

Botón con efectos avanzados y micro-interacciones.

```tsx
import { ModernButton, ModernFAB, ModernButtonGroup } from '@/components/ui/modern-button';

// Botón básico
<ModernButton variant="gradient" size="lg" loading={isLoading}>
  Guardar Cambios
</ModernButton>

// Botón flotante
<ModernFAB position="bottom-right" size="lg">
  <Plus className="h-6 w-6" />
</ModernFAB>

// Grupo de botones
<ModernButtonGroup variant="glass">
  <ModernButton variant="ghost">Cancelar</ModernButton>
  <ModernButton variant="default">Confirmar</ModernButton>
</ModernButtonGroup>
```

**Variantes disponibles:**
- `default` - Estilo primario
- `glass` - Efecto glassmorphism
- `neuro` - Efecto neumorphism
- `gradient` - Gradiente moderno
- `glow` - Efecto de brillo

### ModernInput

Input con estados visuales mejorados y animaciones.

```tsx
import { ModernInput, ModernSearchInput, ModernTextarea } from '@/components/ui/modern-input';

// Input básico
<ModernInput
  variant="glass"
  label="Nombre de usuario"
  placeholder="Ingresa tu nombre"
  leftIcon={User}
  clearable
/>

// Input flotante
<ModernInput
  variant="floating"
  label="Email"
  type="email"
  success={isValid}
  error={errorMessage}
/>

// Input de búsqueda
<ModernSearchInput
  placeholder="Buscar certificados..."
  onSearch={handleSearch}
  searchDelay={300}
/>
```

## ✨ Sistema de Animaciones

### Componentes de Animación

```tsx
import {
  AnimateIn,
  ScrollAnimate,
  StaggeredList,
  HoverEffect,
  LoadingSpinner
} from '@/components/ui/animations';

// Animación de entrada
<AnimateIn animation="fade" duration="normal" delay={200}>
  <div>Contenido animado</div>
</AnimateIn>

// Animación en scroll
<ScrollAnimate animation="slide-up" threshold={0.2}>
  <div>Se anima al hacer scroll</div>
</ScrollAnimate>

// Lista escalonada
<StaggeredList delay={100}>
  {items.map(item => <div key={item.id}>{item.name}</div>)}
</StaggeredList>

// Efectos de hover
<HoverEffect effect="lift" intensity="normal">
  <div>Elemento con hover</div>
</HoverEffect>
```

### Animaciones CSS Disponibles

```css
/* Animaciones de entrada */
.animate-fade-in-up
.animate-slide-in-right
.animate-slide-in-left
.animate-scale-in
.animate-bounce-in

/* Efectos de hover */
.hover:scale-105
.hover:-translate-y-2
.hover:shadow-xl

/* Micro-animaciones */
.animate-pulse-subtle
.animate-bounce-subtle
.animate-shimmer
```

## 🎯 Sidebar Moderno

### Características

- **Navegación colapsible** con persistencia
- **Micro-interacciones** en elementos
- **Tooltips informativos** en modo colapsado
- **Indicadores de estado** activo
- **Efectos glassmorphism**

```tsx
import { DashboardSidebar, useSidebarState } from '@/components/dashboard/dashboard-sidebar';

function Layout() {
  const sidebar = useSidebarState();

  return (
    <DashboardSidebar
      isOpen={sidebar.isOpen}
      isCollapsed={sidebar.isCollapsed}
      onClose={sidebar.close}
      onToggleCollapse={sidebar.toggleCollapse}
      userRole="admin"
    />
  );
}
```

## ♿ Accesibilidad

### Componentes Accesibles

```tsx
import {
  SkipLink,
  ScreenReaderOnly,
  AccessibleButton,
  AccessibleModal,
  AccessibleProgress
} from '@/components/ui/accessibility';

// Skip link para navegación por teclado
<SkipLink href="#main-content">
  Saltar al contenido principal
</SkipLink>

// Botón accesible
<AccessibleButton
  variant="primary"
  loading={isLoading}
  loadingText="Guardando..."
  describedBy="help-text"
>
  Guardar
</AccessibleButton>

// Modal accesible
<AccessibleModal
  isOpen={isOpen}
  onClose={onClose}
  title="Confirmar acción"
  description="Esta acción no se puede deshacer"
>
  <div>Contenido del modal</div>
</AccessibleModal>
```

## 📱 Responsive Design

### Componentes Responsive

```tsx
import {
  ResponsiveContainer,
  ResponsiveGrid,
  ResponsiveStack,
  ShowAt,
  HideAt
} from '@/components/ui/responsive';

// Container responsive
<ResponsiveContainer size="xl" padding="lg">
  <div>Contenido centrado</div>
</ResponsiveContainer>

// Grid adaptativo
<ResponsiveGrid
  columns={{ xs: 1, sm: 2, md: 3, lg: 4 }}
  gap="md"
>
  {items.map(item => <div key={item.id}>{item.name}</div>)}
</ResponsiveGrid>

// Stack responsive
<ResponsiveStack
  direction={{ xs: 'col', md: 'row' }}
  gap="lg"
  align="center"
>
  <div>Elemento 1</div>
  <div>Elemento 2</div>
</ResponsiveStack>

// Mostrar/ocultar por breakpoint
<ShowAt breakpoint="md">
  <div>Solo visible en tablet y desktop</div>
</ShowAt>

<HideAt breakpoint="sm">
  <div>Oculto en móvil</div>
</HideAt>
```

## 🎨 Efectos Visuales

### Glassmorphism

```tsx
// Aplicar efecto glass
<div className="glass">
  Contenido con efecto cristal
</div>

<div className="glass-subtle">
  Efecto cristal sutil
</div>
```

### Neumorphism

```tsx
// Efectos neumorphism
<div className="neuro">
  Elemento con relieve
</div>

<div className="neuro-inset">
  Elemento hundido
</div>

<div className="neuro-subtle">
  Efecto sutil
</div>
```

### Tarjetas Modernas

```tsx
// Tarjeta interactiva
<div className="card-modern">
  Tarjeta con hover effects
</div>

// Elemento interactivo
<div className="interactive">
  Elemento con micro-interacciones
</div>
```

## 🔧 Configuración

### Variables CSS Personalizadas

```css
:root {
  /* Duración de animaciones */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;

  /* Efectos glassmorphism */
  --glass-background: 0 0% 100% / 0.8;
  --glass-border: 222.2 84% 4.9% / 0.1;
  --glass-backdrop-blur: 12px;

  /* Sombras modernas */
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 8px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 20px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 25px 35px -8px rgb(0 0 0 / 0.1);
}
```

### Tailwind CSS Extensions

```js
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      animation: {
        'pulse-subtle': 'pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'slide-in-right': 'slide-in-right 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        'fade-in-up': 'fade-in-up 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      },
      backdropBlur: {
        'glass': 'var(--glass-backdrop-blur)',
      },
    },
  },
};
```

## 🚀 Mejores Prácticas

### Performance
- Usar `transform` y `opacity` para animaciones
- Implementar `will-change` para elementos animados
- Lazy loading para imágenes y componentes pesados

### Accesibilidad
- Respetar `prefers-reduced-motion`
- Mantener contraste WCAG 2.1 AA
- Proporcionar alternativas para efectos visuales

### Responsive
- Mobile-first approach
- Breakpoints consistentes
- Contenido adaptativo, no solo layout

### Mantenimiento
- Usar variables CSS para consistencia
- Documentar componentes personalizados
- Testing en múltiples dispositivos y navegadores

## 💡 Ejemplos Prácticos

### Dashboard Moderno Completo

```tsx
import {
  ModernCard,
  StatCard,
  ModernButton,
  AnimateIn,
  StaggeredList,
  ResponsiveGrid
} from '@/components/ui';

function ModernDashboard() {
  const metrics = [
    { title: "Usuarios", value: "1,234", trend: "up", trendValue: "+12%" },
    { title: "Certificados", value: "856", trend: "up", trendValue: "+8%" },
    { title: "Cursos", value: "42", trend: "neutral" },
    { title: "Empresas", value: "18", trend: "up", trendValue: "+2%" },
  ];

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <AnimateIn animation="fade" duration="normal">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground">Vista general del sistema</p>
          </div>
          <ModernButton variant="gradient" size="lg">
            Nuevo Certificado
          </ModernButton>
        </div>
      </AnimateIn>

      {/* Métricas */}
      <ResponsiveGrid columns={{ xs: 1, sm: 2, lg: 4 }} gap="lg">
        <StaggeredList delay={100}>
          {metrics.map((metric, index) => (
            <StatCard
              key={index}
              title={metric.title}
              value={metric.value}
              trend={metric.trend}
              trendValue={metric.trendValue}
              variant="glass"
            />
          ))}
        </StaggeredList>
      </ResponsiveGrid>

      {/* Contenido principal */}
      <ResponsiveGrid columns={{ xs: 1, lg: 3 }} gap="lg">
        <div className="lg:col-span-2">
          <ModernCard variant="glass" className="h-96">
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4">Actividad Reciente</h3>
              {/* Contenido del gráfico */}
            </div>
          </ModernCard>
        </div>

        <ModernCard variant="neuro">
          <div className="p-6">
            <h3 className="text-lg font-semibold mb-4">Acciones Rápidas</h3>
            <div className="space-y-3">
              <ModernButton variant="ghost" className="w-full justify-start">
                Crear Certificado
              </ModernButton>
              <ModernButton variant="ghost" className="w-full justify-start">
                Gestionar Usuarios
              </ModernButton>
              <ModernButton variant="ghost" className="w-full justify-start">
                Ver Reportes
              </ModernButton>
            </div>
          </div>
        </ModernCard>
      </ResponsiveGrid>
    </div>
  );
}
```

### Formulario Moderno

```tsx
import {
  ModernInput,
  ModernButton,
  ModernCard,
  AnimateIn
} from '@/components/ui';

function ModernForm() {
  return (
    <ModernCard variant="glass" size="lg" className="max-w-md mx-auto">
      <div className="p-8 space-y-6">
        <AnimateIn animation="fade" duration="normal">
          <div className="text-center">
            <h2 className="text-2xl font-bold">Crear Cuenta</h2>
            <p className="text-muted-foreground">Únete a QR CURSE</p>
          </div>
        </AnimateIn>

        <div className="space-y-4">
          <ModernInput
            variant="floating"
            label="Nombre completo"
            leftIcon={User}
            clearable
          />

          <ModernInput
            variant="floating"
            label="Email"
            type="email"
            leftIcon={Mail}
            clearable
          />

          <ModernInput
            variant="floating"
            label="Contraseña"
            type="password"
          />
        </div>

        <ModernButton
          variant="gradient"
          size="lg"
          className="w-full"
          ripple
        >
          Crear Cuenta
        </ModernButton>
      </div>
    </ModernCard>
  );
}
```

### Lista Interactiva

```tsx
import {
  ModernCard,
  HoverEffect,
  StaggeredList,
  LoadingSpinner
} from '@/components/ui';

function InteractiveList({ items, loading }) {
  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <LoadingSpinner size="lg" variant="dots" />
      </div>
    );
  }

  return (
    <StaggeredList delay={50} className="space-y-3">
      {items.map((item, index) => (
        <HoverEffect key={item.id} effect="lift" intensity="subtle">
          <ModernCard variant="glass" interactive>
            <div className="p-4 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                  <item.icon className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-medium">{item.title}</h4>
                  <p className="text-sm text-muted-foreground">{item.description}</p>
                </div>
              </div>
              <ModernButton variant="ghost" size="sm">
                Ver más
              </ModernButton>
            </div>
          </ModernCard>
        </HoverEffect>
      ))}
    </StaggeredList>
  );
}
```
