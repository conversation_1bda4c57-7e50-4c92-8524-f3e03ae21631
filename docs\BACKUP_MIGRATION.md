# 🔄 Backup & Migration Guide

> **See Also:** [INDEX.md](./INDEX.md) | [PLANNING.md](./PLANNING.md) | [SECURITY.md](./SECURITY.md)

This guide explains how to backup and migrate your Supabase database and storage for the QR Course Platform Scaffold.

---

## 1. Why Backup & Migrate?
- Safeguard your data
- Clone the platform for new clients/projects
- Enable disaster recovery

---

## 2. Backup Methods

### a) SQL Backup (Recommended)
- Use `backup-supabase.sql` to export schema and data
- Can be restored via Supabase SQL editor or CLI

### b) JSON Backup
- Use `backup-supabase.json` for data-only backup (good for migrations, analytics, or seeding)

### c) Supabase Storage
- Download all files from Supabase Storage via dashboard or CLI

---

## 3. Automating Backups
- Use GitHub Actions to schedule regular exports (see `.github/workflows/ci.yml`)
- Store backups in a secure location (S3, Google Drive, etc.)
- Example: Add a job to run `node backup-supabase.js` and commit to a `backups/` folder

---

## 4. Restoring to a New Supabase Project
1. **Create a new Supabase project**
2. **Import SQL backup**
   - Go to SQL Editor > Run SQL > Paste/upload `backup-supabase.sql`
3. **Restore Storage**
   - Upload files to the new project's storage buckets
4. **Reapply RLS, triggers, and functions**
   - Copy from old project or use Supabase CLI
5. **Update environment variables**
   - Edit `.env.local` with new Supabase credentials

---

## 5. Best Practices
- Test restores regularly
- Keep backups encrypted and offsite
- Document any manual steps (e.g., RLS, triggers)
- Use versioned migrations for schema changes

---

## 6. References
- [Supabase Backups](https://supabase.com/docs/guides/platform/backups)
- [Supabase CLI](https://supabase.com/docs/guides/cli)
- [Supabase Storage](https://supabase.com/docs/guides/storage)

---

## Modernization Roadmap Reference

- See the [README](../README.md#-plan-de-modernización-y-mejora-continua-faseshitos) for the full phased plan.
- This backup/migration guide is Phase 3 of the modernization roadmap.
- All backup and migration automation is cross-referenced in [PLANNING.md](./PLANNING.md), [INDEX.md](./INDEX.md), and [SECURITY.md](./SECURITY.md).