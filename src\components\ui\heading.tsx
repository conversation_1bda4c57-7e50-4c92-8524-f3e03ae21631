"use client";

import React from "react";
import { cn } from "@/lib/utils";

interface HeadingProps {
  level: 1 | 2 | 3 | 4 | 5 | 6;
  children: React.ReactNode;
  className?: string;
}

/**
 * Componente accesible para encabezados que garantiza una jerarquía correcta
 */
export function Heading({ level, children, className }: HeadingProps) {
  const Tag = `h${level}` as keyof JSX.IntrinsicElements;

  // Estilos basados en el nivel del encabezado
  const styles = {
    1: "text-4xl font-extrabold tracking-tight sm:text-5xl lg:text-6xl",
    2: "text-3xl font-bold",
    3: "text-2xl font-bold",
    4: "text-xl font-semibold",
    5: "text-lg font-medium",
    6: "text-base font-medium",
  };

  return <Tag className={cn(styles[level], className)}>{children}</Tag>;
}

/**
 * Componente para crear una sección con encabezado y contenido
 * que garantiza la jerarquía correcta de encabezados
 */
export function Section({
  level,
  title,
  children,
  titleClassName,
  className,
}: {
  level: 1 | 2 | 3 | 4 | 5;
  title: React.ReactNode;
  children: React.ReactNode;
  titleClassName?: string;
  className?: string;
}) {
  return (
    <section className={className}>
      <Heading level={level} className={titleClassName}>
        {title}
      </Heading>
      {children}
    </section>
  );
}
