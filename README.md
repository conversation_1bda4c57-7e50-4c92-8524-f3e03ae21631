# CSI Online Course & Certification Platform

Lanza, gestiona y certifica a tu fuerza laboral con una plataforma moderna y robusta para cursos y certificaciones online.

---

## Descripción General

Esta plantilla genérica permite a cualquier organización crear, administrar y certificar cursos online de manera eficiente y segura. Incluye:

- Gestión integral de usuarios, cursos y certificados
- Paneles para administradores, instructores y estudiantes
- Emisión y verificación de certificados digitales con QR
- Sistema de plantillas personalizables para certificados
- Seguridad avanzada y cumplimiento de buenas prácticas
- Interfaz moderna, responsive y accesible
- Automatización de calidad de código y despliegue

> **Nota:** Esta plantilla es genérica y no incluye información de contacto ni branding específico. Personalízala según las necesidades de tu organización.

---

## Características Principales

- **Gestión de cursos y usuarios**: Crea, edita y asigna cursos a tu equipo o clientes.
- **Certificación digital**: Emite certificados con QR verificable y control de validez.
- **Paneles diferenciados**: Acceso personalizado para administradores, instructores y estudiantes.
- **Automatización**: Validación de calidad de código, despliegue continuo y backups automáticos.
- **Seguridad**: Autenticación robusta, control de roles y políticas de acceso.
- **Personalización**: Sistema de plantillas para adaptar certificados y branding.

---

## ¿Cómo empezar?

1. Clona este repositorio y sigue la documentación incluida.
2. Personaliza la configuración, branding y plantillas según tu organización.
3. Despliega en tu infraestructura preferida (Vercel, Supabase, etc.).

---

## Documentación

- [PLANNING.md](docs/PLANNING.md): Constitución y visión del proyecto
- [TASKS.md](docs/TASKS.md): Gestión de tareas y roadmap
- [DESIGN_SYSTEM.md](docs/DESIGN_SYSTEM.md): Sistema de diseño y personalización
- [SECURITY.md](docs/SECURITY.md): Buenas prácticas de seguridad
- [AI_CONTEXT.md](docs/AI_CONTEXT.md): Integración con agentes AI

---

> Esta plantilla sigue el enfoque de context-engineering-template para máxima claridad, modularidad y compatibilidad con agentes AI.
