name: <PERSON><PERSON><PERSON>, Auto-fix & Notify

on:
  pull_request:
    branches: [main, develop]

jobs:
  biome-lint-autofix:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Run Biome check
        id: biome_check
        run: |
          yarn biome check . > biome-report.txt || true

      - name: Run Biome auto-fix
        id: biome_fix
        run: |
          yarn biome format . --write

      - name: Commit and push Biome fixes (if any)
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
          git add .
          if ! git diff --cached --quiet; then
            git commit -m 'chore(biome): auto-fix lint/format issues [ci skip]'
            git push origin HEAD:${{ github.head_ref }}
          fi

      - name: Post Biome report as PR comment
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          path: biome-report.txt
          header: |
            🚦 **Biome Lint & Format Report** 🚦

            > Este reporte muestra los resultados del análisis automático de linting y formato realizado por Biome en tu PR. Si ves errores aquí, corrígelos o acepta los auto-fixes propuestos.

            - **Linting y formato ejecutados automáticamente**
            - **Auto-fix aplicado y commit push si fue posible**
            - **El PR no podrá ser mergeado si quedan errores**

            _Para más detalles sobre reglas y convenciones, consulta la documentación del proyecto._

      - name: Fail if Biome still finds issues
        id: biome_fail
        run: |
          yarn biome check .

      # El paso de notificación a Discord ha sido eliminado para simplificar el workflow.