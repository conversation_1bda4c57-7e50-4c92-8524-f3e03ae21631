"use client";

import { useState } from "react";
import { UserCombobox, type UserOption } from "@/components/ui/user-combobox";

export default function TestCombobox() {
  const [selectedUser, setSelectedUser] = useState("");

  // Sample user data
  const users: UserOption[] = [
    {
      value: "1",
      label: "<PERSON>",
      email: "<EMAIL>",
      identity_document: "12345678-9",
    },
    {
      value: "2",
      label: "<PERSON>",
      email: "<EMAIL>",
      identity_document: "98765432-1",
    },
    {
      value: "3",
      label: "<PERSON>",
      email: "<EMAIL>",
      identity_document: "45678912-3",
    },
    {
      value: "4",
      label: "<PERSON>",
      email: "<EMAIL>",
      identity_document: "78912345-6",
    },
    {
      value: "5",
      label: "<PERSON>",
      email: "<EMAIL>",
      identity_document: "32165498-7",
    },
  ];

  return (
    <div className="p-8 max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-6">UserCombobox Test</h1>

      <div className="space-y-6">
        <div className="space-y-2">
          <label className="text-sm font-medium">Select a User</label>
          <UserCombobox
            options={users}
            value={selectedUser}
            onChange={setSelectedUser}
            placeholder="Search for a user..."
            emptyMessage="No users found"
          />
        </div>

        <div className="p-4 bg-gray-100 rounded-md">
          <h2 className="text-lg font-semibold mb-2">Selected User</h2>
          {selectedUser ? (
            <div>
              <p>
                <strong>ID:</strong> {selectedUser}
              </p>
              <p>
                <strong>Name:</strong>{" "}
                {users.find((u) => u.value === selectedUser)?.label}
              </p>
              <p>
                <strong>Email:</strong>{" "}
                {users.find((u) => u.value === selectedUser)?.email}
              </p>
              <p>
                <strong>RUT:</strong>{" "}
                {users.find((u) => u.value === selectedUser)?.identity_document}
              </p>
            </div>
          ) : (
            <p className="text-gray-500">No user selected</p>
          )}
        </div>

        <div className="p-4 border border-gray-200 rounded-md">
          <h2 className="text-lg font-semibold mb-2">Debug Info</h2>
          <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto">
            {JSON.stringify({ selectedUser, users }, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
}
