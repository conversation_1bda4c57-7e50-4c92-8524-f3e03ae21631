import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  env: {
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || "https://mrgcukvyvivpsacohdqh.supabase.co",
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1yZ2N1a3Z5dml2cHNhY29oZHFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE4NTI2MDIsImV4cCI6MjA1NzQyODYwMn0.UHN1JujAjGStmJ37gVRKgRoAwhiJHShzV7RU4cy5t-4",
  },
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: ["mrgcukvyvivpsacohdqh.supabase.co"],
  },
};

export default nextConfig;
