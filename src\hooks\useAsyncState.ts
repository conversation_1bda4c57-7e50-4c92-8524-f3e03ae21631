"use client";

import { useState, useCallback, useRef, useEffect } from "react";

export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  success: boolean;
}

export interface AsyncActions<T> {
  execute: (...args: any[]) => Promise<T>;
  reset: () => void;
  setData: (data: T) => void;
  setError: (error: Error) => void;
}

export interface UseAsyncStateOptions {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  retryCount?: number;
  retryDelay?: number;
}

/**
 * Hook para manejar estados asíncronos con loading, error y success
 */
export function useAsyncState<T = any>(
  asyncFunction: (...args: any[]) => Promise<T>,
  options: UseAsyncStateOptions = {}
): [AsyncState<T>, AsyncActions<T>] {
  const {
    immediate = false,
    onSuccess,
    onError,
    retryCount = 0,
    retryDelay = 1000
  } = options;

  const [state, setState] = useState<AsyncState<T>>({
    data: null,
    loading: false,
    error: null,
    success: false,
  });

  const mountedRef = useRef(true);
  const retryCountRef = useRef(0);

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const execute = useCallback(
    async (...args: any[]): Promise<T> => {
      if (!mountedRef.current) {
        return Promise.resolve(null as T);
      }

      setState(prev => ({
        ...prev,
        loading: true,
        error: null,
        success: false,
      }));

      try {
        const result = await asyncFunction(...args);

        if (!mountedRef.current) {
          return result;
        }

        setState({
          data: result,
          loading: false,
          error: null,
          success: true,
        });

        retryCountRef.current = 0;
        onSuccess?.(result);
        return result;
      } catch (error) {
        if (!mountedRef.current) {
          return Promise.resolve(null as T);
        }

        const errorObj = error instanceof Error ? error : new Error(String(error));

        // Retry logic
        if (retryCountRef.current < retryCount) {
          retryCountRef.current++;

          setTimeout(() => {
            if (mountedRef.current) {
              execute(...args);
            }
          }, retryDelay);

          return Promise.resolve(null as T);
        }

        setState({
          data: null,
          loading: false,
          error: errorObj,
          success: false,
        });

        onError?.(errorObj);
        return Promise.resolve(null as T);
      }
    },
    [asyncFunction, onSuccess, onError, retryCount, retryDelay]
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false,
    });
    retryCountRef.current = 0;
  }, []);

  const setData = useCallback((data: T) => {
    setState(prev => ({
      ...prev,
      data,
      success: true,
      error: null,
    }));
  }, []);

  const setError = useCallback((error: Error) => {
    setState(prev => ({
      ...prev,
      error,
      success: false,
      loading: false,
    }));
  }, []);

  // Execute immediately if requested
  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [immediate, execute]);

  return [
    state,
    {
      execute,
      reset,
      setData,
      setError,
    },
  ];
}

/**
 * Hook simplificado para operaciones CRUD
 */
export function useCrudState<T = any>() {
  const [createState, createActions] = useAsyncState<T>(async () => {
    throw new Error("Create function not implemented");
  });

  const [readState, readActions] = useAsyncState<T[]>(async () => {
    throw new Error("Read function not implemented");
  });

  const [updateState, updateActions] = useAsyncState<T>(async () => {
    throw new Error("Update function not implemented");
  });

  const [deleteState, deleteActions] = useAsyncState<boolean>(async () => {
    throw new Error("Delete function not implemented");
  });

  const isLoading = createState.loading || readState.loading || updateState.loading || deleteState.loading;
  const hasError = createState.error || readState.error || updateState.error || deleteState.error;

  return {
    create: { state: createState, actions: createActions },
    read: { state: readState, actions: readActions },
    update: { state: updateState, actions: updateActions },
    delete: { state: deleteState, actions: deleteActions },
    isLoading,
    hasError,
  };
}

/**
 * Hook para manejar múltiples operaciones asíncronas
 */
export function useMultipleAsync<T extends Record<string, (...args: any[]) => Promise<any>>>(
  operations: T
): {
  [K in keyof T]: {
    state: AsyncState<Awaited<ReturnType<T[K]>>>;
    execute: T[K];
  };
} & {
  isAnyLoading: boolean;
  hasAnyError: boolean;
  resetAll: () => void;
} {
  const results = {} as any;
  const resetFunctions: (() => void)[] = [];
  let isAnyLoading = false;
  let hasAnyError = false;

  for (const [key, operation] of Object.entries(operations)) {
    const [state, actions] = useAsyncState(operation);
    results[key] = {
      state,
      execute: actions.execute,
    };
    resetFunctions.push(actions.reset);

    if (state.loading) isAnyLoading = true;
    if (state.error) hasAnyError = true;
  }

  const resetAll = useCallback(() => {
    resetFunctions.forEach(reset => reset());
  }, [resetFunctions]);

  return {
    ...results,
    isAnyLoading,
    hasAnyError,
    resetAll,
  };
}
