/**
 * @fileoverview Certificate Service - Business logic for certificate management
 *
 * This service provides comprehensive certificate management functionality including
 * creation, validation, verification, and lifecycle management. It handles QR Curse
 * generation, certificate numbering, expiration tracking, and public verification.
 *
 * Features:
 * - Certificate creation with automatic numbering
 * - QR Curse generation and management
 * - Certificate verification and validation
 * - Expiration tracking and notifications
 * - Bulk certificate operations
 * - Public verification API
 * - Certificate templates support
 * - Audit trail and history tracking
 *
 * @example Basic Certificate Operations
 * ```typescript
 * import { certificateService } from '@/lib/services';
 *
 * // Create a new certificate
 * const createResult = await certificateService.createCertificate({
 *   user_id: 'user-uuid',
 *   course_id: 'course-uuid',
 *   attendance_percentage: 95,
 *   template_id: 'template-uuid'
 * });
 *
 * if (createResult.success) {
 *   console.log('Certificate created:', createResult.data.certificate_number);
 * }
 *
 * // Verify a certificate by QR Curse
 * const verifyResult = await certificateService.verifyCertificateByQR('qr-code-data');
 * if (verifyResult.success && verifyResult.data.valid) {
 *   console.log('Certificate is valid');
 * }
 * ```
 *
 * @example Certificate Search and Filtering
 * ```typescript
 * // Search certificates with filters
 * const searchResult = await certificateService.searchCertificates({
 *   user_id: 'user-uuid',
 *   status: 'active',
 *   page: 1,
 *   limit: 10,
 *   orderBy: 'issue_date',
 *   orderDirection: 'desc'
 * });
 *
 * if (searchResult.success) {
 *   const { data, pagination } = searchResult.data;
 *   console.log(`Found ${pagination.total} certificates`);
 * }
 * ```
 *
 * <AUTHOR> CURSE Development Team
 * @version 1.0.0
 * @since 2025-01-12
 */

import type { Certificate } from "../adapters/database/types";
import {
  BaseService,
  type PaginatedResponse,
  type PaginationOptions,
  type ServiceResponse,
} from "./base-service";

// ============================================================================
// Certificate Service Types
// ============================================================================

/**
 * Request payload for creating new certificates
 *
 * @example
 * ```typescript
 * const createRequest: CreateCertificateRequest = {
 *   user_id: 'user-uuid-123',
 *   course_id: 'course-uuid-456',
 *   attendance_percentage: 95,
 *   template_id: 'template-uuid-789',
 *   expiry_date: '2025-12-31'
 * };
 * ```
 */
export interface CreateCertificateRequest {
  /** ID of the user receiving the certificate */
  user_id: string;
  /** ID of the course for which the certificate is issued */
  course_id: string;
  /** Custom certificate number (auto-generated if not provided) */
  certificate_number?: string;
  /** Issue date in ISO format (defaults to current date) */
  issue_date?: string;
  /** Expiry date in ISO format (optional) */
  expiry_date?: string;
  /** Student's attendance percentage for the course */
  attendance_percentage?: number;
  /** ID of the certificate template to use */
  template_id?: string;
  /** QR Curse data (auto-generated if not provided) */
  qr_code?: string;
}

/**
 * Request payload for updating existing certificates
 *
 * @example
 * ```typescript
 * const updateRequest: UpdateCertificateRequest = {
 *   expiry_date: '2026-12-31',
 *   attendance_percentage: 98,
 *   status: 'active'
 * };
 * ```
 */
export interface UpdateCertificateRequest {
  /** Updated expiry date */
  expiry_date?: string;
  /** Updated attendance percentage */
  attendance_percentage?: number;
  /** Updated template ID */
  template_id?: string;
  /** Updated certificate status */
  status?: "active" | "revoked" | "expired";
}

/**
 * Search and filter options for certificate queries
 *
 * @example
 * ```typescript
 * const searchOptions: CertificateSearchOptions = {
 *   user_id: 'user-uuid',
 *   status: 'active',
 *   issue_date_from: '2024-01-01',
 *   issue_date_to: '2024-12-31',
 *   page: 1,
 *   limit: 20,
 *   orderBy: 'issue_date',
 *   orderDirection: 'desc'
 * };
 * ```
 */
export interface CertificateSearchOptions extends PaginationOptions {
  /** Filter by specific user ID */
  user_id?: string;
  /** Filter by specific course ID */
  course_id?: string;
  /** Filter by certificate status */
  status?: "active" | "revoked" | "expired";
  /** Filter by issue date from (inclusive) */
  issue_date_from?: string;
  /** Filter by issue date to (inclusive) */
  issue_date_to?: string;
}

/**
 * Result of certificate verification operations
 *
 * @example Valid Certificate
 * ```typescript
 * const verificationResult: CertificateVerificationResult = {
 *   valid: true,
 *   certificate: {
 *     id: 'cert-uuid',
 *     certificate_number: 'CERT-2024-001',
 *     user: { first_name: 'John', last_name: 'Doe' },
 *     course: { name: 'Web Development Fundamentals' }
 *   },
 *   verification_date: '2024-01-15T10:30:00Z'
 * };
 * ```
 *
 * @example Invalid Certificate
 * ```typescript
 * const verificationResult: CertificateVerificationResult = {
 *   valid: false,
 *   reason: 'Certificate has been revoked',
 *   verification_date: '2024-01-15T10:30:00Z'
 * };
 * ```
 */
export interface CertificateVerificationResult {
  /** Whether the certificate is valid */
  valid: boolean;
  /** Certificate data with related user and course information */
  certificate?: Certificate & {
    user?: any;
    course?: any;
  };
  /** Reason for invalid certificates */
  reason?: string;
  /** Timestamp of verification */
  verification_date: string;
}

export interface BulkIssueCertificatesRequest {
  course_id: string;
  user_ids: string[];
  template_id?: string;
  issue_date?: string;
  expiry_date?: string;
}

// ============================================================================
// Certificate Service Implementation
// ============================================================================

export class CertificateService extends BaseService {
  // ============================================================================
  // Certificate CRUD Operations
  // ============================================================================

  async issueCertificate(
    request: CreateCertificateRequest,
  ): Promise<ServiceResponse<Certificate>> {
    this.logInfo("Issuing certificate", {
      user_id: request.user_id,
      course_id: request.course_id,
    });

    // Validation
    const validationError =
      await this.validateCreateCertificateRequest(request);
    if (validationError) {
      return validationError;
    }

    // Check if user already has a certificate for this course
    const existingCertificates =
      await this.repositories.certificates.findByUser(request.user_id);
    const duplicateCertificate = existingCertificates.find(
      (cert) =>
        cert.course_id === request.course_id && cert.status === "active",
    );

    if (duplicateCertificate) {
      return this.conflictError(
        "User already has an active certificate for this course",
      );
    }

    return this.handleRepositoryOperation(async () => {
      const certificateData = {
        ...request,
        issue_date: request.issue_date || this.formatDate(new Date()),
        status: "active" as const,
        qr_code: request.qr_code || (await this.generateQRCode(request)),
        created_at: this.formatDate(new Date()),
        updated_at: this.formatDate(new Date()),
      };

      return await this.repositories.certificates.issueCertificate(
        certificateData,
      );
    }, "Failed to issue certificate");
  }

  async getCertificateById(id: string): Promise<ServiceResponse<Certificate>> {
    const validationError = this.validateUUID(id, "certificate_id");
    if (validationError) {
      return validationError;
    }

    return this.handleRepositoryOperation(async () => {
      const certificate = await this.repositories.certificates.findById(id);
      if (!certificate) {
        throw new Error("Certificate not found");
      }
      return certificate;
    }, "Failed to get certificate");
  }

  async updateCertificate(
    id: string,
    request: UpdateCertificateRequest,
  ): Promise<ServiceResponse<Certificate>> {
    this.logInfo("Updating certificate", { id, updates: Object.keys(request) });

    const validationError = this.validateUUID(id, "certificate_id");
    if (validationError) {
      return validationError;
    }

    // Check if certificate exists
    const existingCertificate =
      await this.repositories.certificates.findById(id);
    if (!existingCertificate) {
      return this.notFoundError("Certificate", id);
    }

    return this.handleRepositoryOperation(async () => {
      const updateData = {
        ...request,
        updated_at: this.formatDate(new Date()),
      };

      // Remove undefined values
      Object.keys(updateData).forEach((key) => {
        if (updateData[key as keyof typeof updateData] === undefined) {
          delete updateData[key as keyof typeof updateData];
        }
      });

      return await this.repositories.certificates.update(id, updateData);
    }, "Failed to update certificate");
  }

  // ============================================================================
  // Certificate Verification
  // ============================================================================

  async verifyCertificate(
    certificateNumber: string,
  ): Promise<ServiceResponse<CertificateVerificationResult>> {
    this.logInfo("Verifying certificate", { certificateNumber });

    if (!certificateNumber || certificateNumber.trim() === "") {
      return this.validationError(
        "certificate_number",
        "Certificate number is required",
      );
    }

    return this.handleRepositoryOperation(async () => {
      const verificationResult =
        await this.repositories.certificates.verifyCertificate(
          certificateNumber.trim(),
        );

      // If certificate is valid, get additional details
      if (verificationResult.valid && verificationResult.certificate) {
        try {
          // Get user details
          const user = await this.repositories.users.findById(
            verificationResult.certificate.user_id,
          );

          // Get course details (assuming we have a courses repository)
          const course = await this.repositories.courses.findById(
            verificationResult.certificate.course_id,
          );

          return {
            ...verificationResult,
            certificate: {
              ...verificationResult.certificate,
              user,
              course,
            },
            verification_date: this.formatDate(new Date()),
          };
        } catch (error) {
          // If we can't get additional details, return basic verification
          this.logWarn("Could not fetch certificate details", error);
        }
      }

      return {
        ...verificationResult,
        verification_date: this.formatDate(new Date()),
      };
    }, "Failed to verify certificate");
  }

  async verifyCertificateByQR(
    qrCode: string,
  ): Promise<ServiceResponse<CertificateVerificationResult>> {
    this.logInfo("Verifying certificate by QR Curse");

    if (!qrCode || qrCode.trim() === "") {
      return this.validationError("qr_code", "QR Curse is required");
    }

    return this.handleRepositoryOperation(async () => {
      const certificate = await this.repositories.certificates.findByQRCode(
        qrCode.trim(),
      );

      if (!certificate) {
        return {
          valid: false,
          reason: "Certificate not found",
          verification_date: this.formatDate(new Date()),
        };
      }

      // Use the certificate number to perform full verification
      const verificationResult =
        await this.repositories.certificates.verifyCertificate(
          certificate.certificate_number,
        );

      return {
        ...verificationResult,
        verification_date: this.formatDate(new Date()),
      };
    }, "Failed to verify certificate by QR Curse");
  }

  // ============================================================================
  // Certificate Lifecycle Management
  // ============================================================================

  async revokeCertificate(
    certificateId: string,
    reason?: string,
  ): Promise<ServiceResponse<Certificate>> {
    this.logInfo("Revoking certificate", { certificateId, reason });

    const validationError = this.validateUUID(certificateId, "certificate_id");
    if (validationError) {
      return validationError;
    }

    return this.handleRepositoryOperation(async () => {
      const certificate =
        await this.repositories.certificates.revokeCertificate(
          certificateId,
          reason,
        );

      if (!certificate) {
        throw new Error("Certificate not found");
      }

      return certificate;
    }, "Failed to revoke certificate");
  }

  async renewCertificate(
    certificateId: string,
    newExpiryDate: string,
  ): Promise<ServiceResponse<Certificate>> {
    this.logInfo("Renewing certificate", { certificateId, newExpiryDate });

    const validationError = this.validateUUID(certificateId, "certificate_id");
    if (validationError) {
      return validationError;
    }

    if (!this.isValidDate(newExpiryDate)) {
      return this.validationError("expiry_date", "Invalid expiry date format");
    }

    return this.handleRepositoryOperation(async () => {
      const certificate = await this.repositories.certificates.renewCertificate(
        certificateId,
        newExpiryDate,
      );

      if (!certificate) {
        throw new Error("Certificate not found");
      }

      return certificate;
    }, "Failed to renew certificate");
  }

  // ============================================================================
  // Bulk Operations
  // ============================================================================

  async bulkIssueCertificates(request: BulkIssueCertificatesRequest): Promise<
    ServiceResponse<{
      issued: Certificate[];
      errors: { user_id: string; error: string }[];
    }>
  > {
    this.logInfo("Bulk issuing certificates", {
      course_id: request.course_id,
      user_count: request.user_ids.length,
    });

    const issued: Certificate[] = [];
    const errors: { user_id: string; error: string }[] = [];

    for (const userId of request.user_ids) {
      try {
        const certificateRequest: CreateCertificateRequest = {
          user_id: userId,
          course_id: request.course_id,
          template_id: request.template_id,
          issue_date: request.issue_date,
          expiry_date: request.expiry_date,
        };

        const result = await this.issueCertificate(certificateRequest);

        if (result.success && result.data) {
          issued.push(result.data);
        } else {
          errors.push({
            user_id: userId,
            error: result.error?.message || "Unknown error",
          });
        }
      } catch (error: any) {
        errors.push({
          user_id: userId,
          error: error.message || "Unknown error",
        });
      }
    }

    return this.success(
      {
        issued,
        errors,
      },
      `Bulk operation completed: ${issued.length} issued, ${errors.length} errors`,
    );
  }

  // ============================================================================
  // Search and Statistics
  // ============================================================================

  async searchCertificates(
    options: CertificateSearchOptions = {},
  ): Promise<ServiceResponse<PaginatedResponse<Certificate>>> {
    const { page, limit, offset } = this.validatePaginationOptions(options);

    return this.handleRepositoryOperation(async () => {
      // Build filters
      const filters = [];

      if (options.user_id) {
        filters.push({
          column: "user_id",
          operator: "eq" as const,
          value: options.user_id,
        });
      }

      if (options.course_id) {
        filters.push({
          column: "course_id",
          operator: "eq" as const,
          value: options.course_id,
        });
      }

      if (options.status) {
        filters.push({
          column: "status",
          operator: "eq" as const,
          value: options.status,
        });
      }

      if (options.issue_date_from) {
        filters.push({
          column: "issue_date",
          operator: "gte" as const,
          value: options.issue_date_from,
        });
      }

      if (options.issue_date_to) {
        filters.push({
          column: "issue_date",
          operator: "lte" as const,
          value: options.issue_date_to,
        });
      }

      // Get total count
      const total = await this.repositories.certificates.count(filters);

      // Get paginated results
      const certificates = await this.repositories.certificates.findMany(
        filters,
        {
          limit,
          offset,
          orderBy: options.orderBy
            ? [
                {
                  column: options.orderBy,
                  ascending: options.orderDirection !== "desc",
                },
              ]
            : [{ column: "issue_date", ascending: false }],
        },
      );

      return {
        data: certificates,
        pagination: this.calculatePagination(total, page, limit),
      };
    }, "Failed to search certificates");
  }

  async getCertificateStatistics(): Promise<
    ServiceResponse<{
      total: number;
      active: number;
      expired: number;
      revoked: number;
      recentlyIssued: number;
    }>
  > {
    return this.handleRepositoryOperation(async () => {
      const stats = await this.repositories.certificates.getCertificateStats();

      // Get recently issued certificates (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recentCertificates =
        await this.repositories.certificates.findCertificatesByDateRange(
          thirtyDaysAgo.toISOString(),
          new Date().toISOString(),
        );

      return {
        ...stats,
        recentlyIssued: recentCertificates.length,
      };
    }, "Failed to get certificate statistics");
  }

  // ============================================================================
  // Helper Methods
  // ============================================================================

  private async generateQRCode(
    request: CreateCertificateRequest,
  ): Promise<string> {
    // Generate a unique QR Curse identifier
    // In a real implementation, this might encode certificate verification URL
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `QR-${request.user_id.slice(-8)}-${request.course_id.slice(-8)}-${timestamp}-${random}`;
  }

  private async validateCreateCertificateRequest(
    request: CreateCertificateRequest,
  ): Promise<ServiceResponse | null> {
    // Required fields
    let error = this.validateRequired(request.user_id, "user_id");
    if (error) return error;

    error = this.validateRequired(request.course_id, "course_id");
    if (error) return error;

    // UUID validation
    error = this.validateUUID(request.user_id, "user_id");
    if (error) return error;

    error = this.validateUUID(request.course_id, "course_id");
    if (error) return error;

    // Date validation
    if (request.issue_date && !this.isValidDate(request.issue_date)) {
      return this.validationError("issue_date", "Invalid issue date format");
    }

    if (request.expiry_date && !this.isValidDate(request.expiry_date)) {
      return this.validationError("expiry_date", "Invalid expiry date format");
    }

    // Attendance percentage validation
    if (request.attendance_percentage !== undefined) {
      if (
        request.attendance_percentage < 0 ||
        request.attendance_percentage > 100
      ) {
        return this.validationError(
          "attendance_percentage",
          "Attendance percentage must be between 0 and 100",
        );
      }
    }

    // Check if user exists
    const user = await this.repositories.users.findById(request.user_id);
    if (!user) {
      return this.validationError("user_id", "User not found");
    }

    // Check if course exists
    const course = await this.repositories.courses.findById(request.course_id);
    if (!course) {
      return this.validationError("course_id", "Course not found");
    }

    return null;
  }
}
