"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Users,
  Award,
  BookOpen,
  UserCheck,
  BarChart3,
  Settings,
  LogOut,
  Menu,
  X,
  Home,
  ChevronDown
} from "lucide-react";
import { ThemeToggle } from "@/components/theme/theme-toggle";
import { ModernButton } from "@/components/ui/modern-button";
import { AnimateIn } from "@/components/ui/animations";
import { supabase } from "@/lib/supabase";

// Definir los elementos de navegación
const navigationItems = [
  {
    name: "Dashboard",
    href: "/panel-admin",
    icon: Home,
    current: false,
  },
  {
    name: "Alumnos",
    href: "/panel-admin/alumnos",
    icon: Users,
    current: false,
  },
  {
    name: "Certificados",
    href: "/panel-admin/certificados",
    icon: Award,
    current: false,
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    href: "/panel-admin/cursos",
    icon: BookOpen,
    current: false,
  },
  {
    name: "Instructores",
    href: "/panel-admin/instructores",
    icon: UserCheck,
    current: false,
  },
  {
    name: "Reportes",
    href: "/panel-admin/reportes",
    icon: BarChart3,
    current: false,
  },
];

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Este efecto asegura que el componente solo se renderice en el cliente
  useEffect(() => {
    setMounted(true);
  }, []);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    await supabase.auth.signOut();
    router.push("/login");
  };

  // Actualizar el estado current de los elementos de navegación
  const updatedNavigationItems = navigationItems.map(item => ({
    ...item,
    current: pathname === item.href || (item.href !== "/panel-admin" && pathname.startsWith(item.href))
  }));

  // Si no está montado, devolvemos un div vacío para evitar errores de hidratación
  if (!mounted) {
    return <div className="min-h-screen bg-background"></div>;
  }

  return (
    <div className="min-h-screen bg-background flex">
      {/* Sidebar */}
      <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0">
        <div className="flex flex-col flex-grow bg-card border-r border-border pt-5 pb-4 overflow-y-auto">
          {/* Logo */}
          <div className="flex items-center flex-shrink-0 px-4">
            <Link href="/panel-admin" className="flex items-center">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
                <BarChart3 className="w-5 h-5 text-primary-foreground" />
              </div>
              <span className="text-xl font-bold text-foreground">QR CURSE</span>
            </Link>
          </div>

          {/* Navigation */}
          <nav className="mt-8 flex-1 px-2 space-y-1">
            {updatedNavigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`${
                    item.current
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:bg-muted hover:text-foreground"
                  } group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors`}
                >
                  <Icon
                    className={`${
                      item.current ? "text-primary-foreground" : "text-muted-foreground group-hover:text-foreground"
                    } mr-3 flex-shrink-0 h-5 w-5 transition-colors`}
                  />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* Bottom section */}
          <div className="flex-shrink-0 px-2 space-y-1">
            <div className="border-t border-border pt-4">
              <div className="flex items-center justify-between px-3 py-2">
                <ThemeToggle variant="ghost" size="sm" />
                <button
                  onClick={handleLogout}
                  disabled={isLoggingOut}
                  className="p-2 text-muted-foreground hover:text-foreground hover:bg-muted rounded-lg transition-colors"
                  title="Cerrar sesión"
                >
                  <LogOut className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div className="fixed inset-0 flex z-40 lg:hidden">
          <div className="fixed inset-0 bg-background/80 backdrop-blur-sm" onClick={() => setSidebarOpen(false)} />
          <div className="relative flex-1 flex flex-col max-w-xs w-full bg-card">
            <div className="absolute top-0 right-0 -mr-12 pt-2">
              <button
                className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
                onClick={() => setSidebarOpen(false)}
              >
                <X className="h-6 w-6 text-foreground" />
              </button>
            </div>
            {/* Mobile navigation content - same as desktop */}
            <div className="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
              <div className="flex-shrink-0 flex items-center px-4">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
                  <BarChart3 className="w-5 h-5 text-primary-foreground" />
                </div>
                <span className="text-xl font-bold text-foreground">QR CURSE</span>
              </div>
              <nav className="mt-8 px-2 space-y-1">
                {updatedNavigationItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`${
                        item.current
                          ? "bg-primary text-primary-foreground"
                          : "text-muted-foreground hover:bg-muted hover:text-foreground"
                      } group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors`}
                      onClick={() => setSidebarOpen(false)}
                    >
                      <Icon className="mr-3 flex-shrink-0 h-5 w-5" />
                      {item.name}
                    </Link>
                  );
                })}
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Main content */}
      <div className="lg:pl-64 flex flex-col flex-1">
        {/* Top bar for mobile */}
        <div className="sticky top-0 z-10 lg:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-background border-b border-border">
          <button
            className="h-12 w-12 inline-flex items-center justify-center rounded-md text-muted-foreground hover:text-foreground focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>
        </div>

        {/* Page content */}
        <main className="flex-1">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <AnimateIn animation="fade" duration="normal">
                {children}
              </AnimateIn>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
