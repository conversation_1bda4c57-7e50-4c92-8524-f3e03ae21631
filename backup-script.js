const { Client } = require('pg');
const fs = require('fs');

const connectionString = "postgresql://postgres:BMpnM&d*<EMAIL>:6543/postgres";

async function backupDatabase() {
  const client = new Client({
    connectionString: connectionString,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Get all tables
    const tablesQuery = `
      SELECT tablename
      FROM pg_tables
      WHERE schemaname = 'public'
      AND tablename NOT LIKE 'pg_%'
      AND tablename NOT LIKE 'sql_%'
      ORDER BY tablename;
    `;

    const tablesResult = await client.query(tablesQuery);
    const tables = tablesResult.rows.map(row => row.tablename);

    console.log('Found tables:', tables);

    let backupContent = '-- Database Backup\n';
    backupContent += '-- Generated on: ' + new Date().toISOString() + '\n\n';

    // For each table, get the data
    for (const table of tables) {
      console.log(`Backing up table: ${table}`);

      // Get table structure
      const structureQuery = `
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = $1
        ORDER BY ordinal_position;
      `;

      const structureResult = await client.query(structureQuery, [table]);

      backupContent += `-- Table: ${table}\n`;
      backupContent += `-- Structure\n`;
      backupContent += `CREATE TABLE IF NOT EXISTS "${table}" (\n`;

      const columns = structureResult.rows.map(col => {
        let def = `  "${col.column_name}" ${col.data_type}`;
        if (col.is_nullable === 'NO') def += ' NOT NULL';
        if (col.column_default) def += ` DEFAULT ${col.column_default}`;
        return def;
      });

      backupContent += columns.join(',\n') + '\n);\n\n';

      // Get table data
      const dataQuery = `SELECT * FROM "${table}";`;
      const dataResult = await client.query(dataQuery);

      if (dataResult.rows.length > 0) {
        backupContent += `-- Data for table: ${table}\n`;
        backupContent += `INSERT INTO "${table}" VALUES\n`;

        const values = dataResult.rows.map(row => {
          const rowValues = Object.values(row).map(value => {
            if (value === null) return 'NULL';
            if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
            return value;
          });
          return `  (${rowValues.join(', ')})`;
        });

        backupContent += values.join(',\n') + ';\n\n';
      }
    }

    // Write to file
    fs.writeFileSync('backup.sql', backupContent);
    console.log('Backup completed successfully! File: backup.sql');

  } catch (error) {
    console.error('Error during backup:', error);
  } finally {
    await client.end();
  }
}

backupDatabase();