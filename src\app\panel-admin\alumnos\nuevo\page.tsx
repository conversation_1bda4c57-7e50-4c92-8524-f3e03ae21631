"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
// No direct Supabase client needed here anymore

export default function NuevoAlumnoPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    firstName: "",
    lastName: "",
    identityDocument: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<{
    email?: string;
    password?: string;
    confirmPassword?: string;
    firstName?: string;
    lastName?: string;
    identityDocument?: string;
  }>({});

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Limpiar error específico cuando el usuario edita el campo
    if (validationErrors[name as keyof typeof validationErrors]) {
      setValidationErrors((prev) => ({ ...prev, [name]: undefined }));
    }

    // Limpiar el error general al cambiar cualquier campo
    setError(null);
  };

  const validateForm = () => {
    const errors: {
      email?: string;
      password?: string;
      confirmPassword?: string;
      firstName?: string;
      lastName?: string;
      identityDocument?: string;
    } = {};

    // Validar email
    if (!formData.email) {
      errors.email = "El correo electrónico es obligatorio";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "El correo electrónico no es válido";
    }

    // Validar password
    if (!formData.password) {
      errors.password = "La contraseña es obligatoria";
    } else if (formData.password.length < 6) {
      errors.password = "La contraseña debe tener al menos 6 caracteres";
    }

    // Validar confirmación de password
    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = "Las contraseñas no coinciden";
    }

    // Validar nombre
    if (!formData.firstName) {
      errors.firstName = "El nombre es obligatorio";
    }

    // Validar apellido
    if (!formData.lastName) {
      errors.lastName = "El apellido es obligatorio";
    }

    // Validar documento de identidad
    if (!formData.identityDocument) {
      errors.identityDocument = "El documento de identidad es obligatorio";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Removed checkEmailExists function - API route handles uniqueness

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setValidationErrors({}); // Clear previous validation errors

      // Call the API route to create the student user
      const response = await fetch("/api/admin/create-student", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
          firstName: formData.firstName,
          lastName: formData.lastName,
          identityDocument: formData.identityDocument,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        // Handle errors from the API route
        console.error("API Error Response:", result);
        // Throw an error with the message from the API response to be caught below
        throw new Error(
          result.error || `HTTP error! status: ${response.status}`,
        );
      }

      // If successful, the API route handled creation.
      console.log("API call successful, user created:", result.userId);

      // Redirect on success
      router.push("/panel-admin/alumnos?created=true");
    } catch (err: unknown) {
      // Catch errors from fetch or API response
      console.error("Error completo durante el registro via API:", err);

      let errorMessage =
        "Error al registrar el alumno. Por favor, intenta nuevamente.";
      let isValidationError = false;

      // Extract message safely using type guards
      let message = "Error desconocido durante el registro.";
      if (err instanceof Error) {
        message = err.message;
      } else if (typeof err === "string") {
        message = err;
      }

      // Handle specific errors based on the message from the API or fetch error
      // Match messages returned by the API route
      if (message) {
        if (message.includes("Este correo electrónico ya está registrado")) {
          errorMessage = message;
          setValidationErrors((prev) => ({ ...prev, email: errorMessage }));
          isValidationError = true;
        } else if (
          message.includes("Password must be at least 6 characters long")
        ) {
          errorMessage = "La contraseña debe tener al menos 6 caracteres.";
          setValidationErrors((prev) => ({ ...prev, password: errorMessage }));
          isValidationError = true;
        } else if (message.includes("Email and password are required")) {
          errorMessage = "Email y contraseña son obligatorios.";
          // Potentially set errors for both fields if needed
          setValidationErrors((prev) => ({
            ...prev,
            email: !formData.email ? errorMessage : undefined,
            password: !formData.password ? errorMessage : undefined,
          }));
          isValidationError = true;
        } else {
          // Use the extracted message directly if it's not one of the common ones
          errorMessage = message;
        }
      }

      // Set general error only if it wasn't a specific validation error handled above
      if (!isValidationError) {
        setError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Registrar Nuevo Alumno</h1>
        <button
          onClick={() => router.push("/panel-admin/alumnos")}
          className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Importar Múltiples Alumnos
        </button>
      </div>
      <p className="mb-6 text-gray-600">
        Ingresa los datos del alumno para crear una nueva cuenta. Todos los
        campos son obligatorios. El alumno recibirá un correo electrónico con
        instrucciones para confirmar su cuenta.
      </p>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Datos personales */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label
              htmlFor="firstName"
              className="block text-sm font-medium text-gray-700"
            >
              Nombre
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 focus:ring-indigo-500 focus:border-indigo-500"
              required
            />
            {validationErrors.firstName && (
              <p className="mt-1 text-sm text-red-600">
                {validationErrors.firstName}
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="lastName"
              className="block text-sm font-medium text-gray-700"
            >
              Apellido
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 focus:ring-indigo-500 focus:border-indigo-500"
              required
            />
            {validationErrors.lastName && (
              <p className="mt-1 text-sm text-red-600">
                {validationErrors.lastName}
              </p>
            )}
          </div>
        </div>

        <div>
          <label
            htmlFor="identityDocument"
            className="block text-sm font-medium text-gray-700"
          >
            Documento de Identidad (RUT)
          </label>
          <input
            type="text"
            id="identityDocument"
            name="identityDocument"
            value={formData.identityDocument}
            onChange={handleChange}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 focus:ring-indigo-500 focus:border-indigo-500"
            required
            placeholder="Ej: 12345678-9"
          />
          {validationErrors.identityDocument && (
            <p className="mt-1 text-sm text-red-600">
              {validationErrors.identityDocument}
            </p>
          )}
        </div>

        <div>
          <label
            htmlFor="email"
            className="block text-sm font-medium text-gray-700"
          >
            Correo Electrónico
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 focus:ring-indigo-500 focus:border-indigo-500"
            required
          />
          {validationErrors.email && (
            <p className="mt-1 text-sm text-red-600">
              {validationErrors.email}
            </p>
          )}
        </div>

        <div>
          <label
            htmlFor="password"
            className="block text-sm font-medium text-gray-700"
          >
            Contraseña
          </label>
          <input
            type="password"
            id="password"
            name="password"
            value={formData.password}
            onChange={handleChange}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 focus:ring-indigo-500 focus:border-indigo-500"
            required
            minLength={6}
          />
          <p className="text-xs text-gray-500 mt-1">
            La contraseña debe tener al menos 6 caracteres.
          </p>
          {validationErrors.password && (
            <p className="mt-1 text-sm text-red-600">
              {validationErrors.password}
            </p>
          )}
        </div>

        <div>
          <label
            htmlFor="confirmPassword"
            className="block text-sm font-medium text-gray-700"
          >
            Confirmar Contraseña
          </label>
          <input
            type="password"
            id="confirmPassword"
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 focus:ring-indigo-500 focus:border-indigo-500"
            required
          />
          {validationErrors.confirmPassword && (
            <p className="mt-1 text-sm text-red-600">
              {validationErrors.confirmPassword}
            </p>
          )}
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancelar
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {loading ? "Registrando..." : "Registrar Alumno"}
          </button>
        </div>
      </form>
    </div>
  );
}
