// Reports Service
// Handles data fetching and processing for the reports module

import { supabase } from "@/lib/supabase";
import {
  mockReportMetrics,
  mockCertificatesByMonth,
  mockUsersByRole,
  mockCoursePerformance,
  mockInstructorStats,
  getFilteredMockData,
  type ReportMetrics,
  type CertificatesByMonth,
  type UsersByRole,
  type CoursePerformance,
  type InstructorStats,
} from "@/lib/data/mock-reports-data";

export interface ReportFilters {
  startDate?: string;
  endDate?: string;
  courseId?: string;
  instructorId?: string;
  status?: string;
  role?: string;
}

export interface ReportsData {
  metrics: ReportMetrics;
  certificatesByMonth: CertificatesByMonth[];
  usersByRole: UsersByRole[];
  coursePerformance: CoursePerformance[];
  instructorStats: InstructorStats[];
}

export class ReportsService {
  private useMockData = true; // Toggle for development

  /**
   * Get comprehensive reports data
   */
  async getReportsData(filters: ReportFilters = {}): Promise<ReportsData> {
    if (this.useMockData) {
      return getFilteredMockData(filters);
    }

    try {
      const [
        metrics,
        certificatesByMonth,
        usersByRole,
        coursePerformance,
        instructorStats,
      ] = await Promise.all([
        this.getMetrics(filters),
        this.getCertificatesByMonth(filters),
        this.getUsersByRole(filters),
        this.getCoursePerformance(filters),
        this.getInstructorStats(filters),
      ]);

      return {
        metrics,
        certificatesByMonth,
        usersByRole,
        coursePerformance,
        instructorStats,
      };
    } catch (error) {
      console.error("Error fetching reports data:", error);
      // Fallback to mock data on error
      return getFilteredMockData(filters);
    }
  }

  /**
   * Get general metrics
   */
  private async getMetrics(filters: ReportFilters): Promise<ReportMetrics> {
    try {
      // Get certificates stats
      const { data: certificates, error: certError } = await supabase
        .from("certificates")
        .select("status")
        .gte("created_at", filters.startDate || "2020-01-01")
        .lte("created_at", filters.endDate || "2030-12-31");

      if (certError) throw certError;

      // Get users stats
      const { data: users, error: usersError } = await supabase
        .from("users")
        .select("role, created_at")
        .gte("created_at", filters.startDate || "2020-01-01")
        .lte("created_at", filters.endDate || "2030-12-31");

      if (usersError) throw usersError;

      // Get courses stats
      const { data: courses, error: coursesError } = await supabase
        .from("courses")
        .select("status")
        .gte("created_at", filters.startDate || "2020-01-01")
        .lte("created_at", filters.endDate || "2030-12-31");

      if (coursesError) throw coursesError;

      // Calculate metrics
      const totalCertificates = certificates?.length || 0;
      const activeCertificates = certificates?.filter(c => c.status === "active").length || 0;
      const expiredCertificates = certificates?.filter(c => c.status === "expired").length || 0;
      const revokedCertificates = certificates?.filter(c => c.status === "revoked").length || 0;

      const totalUsers = users?.length || 0;
      const activeUsers = users?.filter(u => u.role !== "inactive").length || 0;

      const totalCourses = courses?.length || 0;
      const completedCourses = courses?.filter(c => c.status === "completed").length || 0;

      return {
        totalCertificates,
        activeCertificates,
        expiredCertificates,
        revokedCertificates,
        totalUsers,
        activeUsers,
        totalCourses,
        completedCourses,
        averageAttendance: 87.5, // TODO: Calculate from attendance table
        averageGrade: 8.2, // TODO: Calculate from grades table
      };
    } catch (error) {
      console.error("Error fetching metrics:", error);
      return mockReportMetrics;
    }
  }

  /**
   * Get certificates by month
   */
  private async getCertificatesByMonth(filters: ReportFilters): Promise<CertificatesByMonth[]> {
    try {
      const { data, error } = await supabase
        .from("certificates")
        .select("created_at, status")
        .gte("created_at", filters.startDate || "2024-01-01")
        .lte("created_at", filters.endDate || "2024-12-31")
        .order("created_at");

      if (error) throw error;

      // Group by month
      const monthlyData: { [key: string]: CertificatesByMonth } = {};

      data?.forEach((cert) => {
        const date = new Date(cert.created_at);
        const monthKey = date.toLocaleDateString('es-ES', { 
          month: 'short', 
          year: 'numeric' 
        });

        if (!monthlyData[monthKey]) {
          monthlyData[monthKey] = {
            month: monthKey,
            certificates: 0,
            active: 0,
            expired: 0,
            revoked: 0,
          };
        }

        monthlyData[monthKey].certificates++;
        
        switch (cert.status) {
          case "active":
            monthlyData[monthKey].active++;
            break;
          case "expired":
            monthlyData[monthKey].expired++;
            break;
          case "revoked":
            monthlyData[monthKey].revoked++;
            break;
        }
      });

      return Object.values(monthlyData);
    } catch (error) {
      console.error("Error fetching certificates by month:", error);
      return mockCertificatesByMonth;
    }
  }

  /**
   * Get users by role
   */
  private async getUsersByRole(filters: ReportFilters): Promise<UsersByRole[]> {
    try {
      const { data, error } = await supabase
        .from("users")
        .select("role")
        .gte("created_at", filters.startDate || "2020-01-01")
        .lte("created_at", filters.endDate || "2030-12-31");

      if (error) throw error;

      const roleCount: { [key: string]: number } = {};
      const total = data?.length || 0;

      data?.forEach((user) => {
        const role = this.translateRole(user.role);
        roleCount[role] = (roleCount[role] || 0) + 1;
      });

      return Object.entries(roleCount).map(([role, count]) => ({
        role,
        count,
        percentage: total > 0 ? (count / total) * 100 : 0,
      }));
    } catch (error) {
      console.error("Error fetching users by role:", error);
      return mockUsersByRole;
    }
  }

  /**
   * Get course performance data
   */
  private async getCoursePerformance(filters: ReportFilters): Promise<CoursePerformance[]> {
    try {
      // This would require complex joins with enrollments, grades, and attendance
      // For now, return mock data
      return mockCoursePerformance;
    } catch (error) {
      console.error("Error fetching course performance:", error);
      return mockCoursePerformance;
    }
  }

  /**
   * Get instructor statistics
   */
  private async getInstructorStats(filters: ReportFilters): Promise<InstructorStats[]> {
    try {
      // This would require complex joins with courses, users, and certificates
      // For now, return mock data
      return mockInstructorStats;
    } catch (error) {
      console.error("Error fetching instructor stats:", error);
      return mockInstructorStats;
    }
  }

  /**
   * Translate role to Spanish
   */
  private translateRole(role: string): string {
    const translations: { [key: string]: string } = {
      student: "Estudiantes",
      instructor: "Instructores",
      admin: "Administradores",
      company_rep: "Representantes",
    };
    return translations[role] || role;
  }

  /**
   * Export data to CSV format
   */
  async exportToCSV(data: any[], filename: string): Promise<string> {
    if (!data.length) return "";

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(","),
      ...data.map(row => 
        headers.map(header => 
          JSON.stringify(row[header] || "")
        ).join(",")
      )
    ].join("\n");

    return csvContent;
  }

  /**
   * Toggle between mock and real data
   */
  setUseMockData(useMock: boolean): void {
    this.useMockData = useMock;
  }
}

// Export singleton instance
export const reportsService = new ReportsService();
