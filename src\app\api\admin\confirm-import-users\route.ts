import { createClient } from "@supabase/supabase-js";
import { NextResponse } from "next/server";
import { generateRandomPassword } from "../import-users/route";

// Initialize Supabase client with SERVICE_ROLE key for admin actions
let supabaseAdmin: ReturnType<typeof createClient> | null = null;
try {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl) {
    console.error(
      "[API Init] Missing environment variable: NEXT_PUBLIC_SUPABASE_URL",
    );
    throw new Error("Server configuration error: Supabase URL missing.");
  }
  if (!serviceKey) {
    console.error(
      "[API Init] Missing environment variable: SUPABASE_SERVICE_ROLE_KEY",
    );
    throw new Error(
      "Server configuration error: Supabase service role key missing.",
    );
  }

  supabaseAdmin = createClient(supabaseUrl, serviceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
  console.log("[API Init] Supabase admin client initialized successfully.");
} catch (initError) {
  console.error(
    "[API Init] Failed to initialize Supabase admin client:",
    initError,
  );
  // supabaseAdmin remains null if initialization fails
}

// We're now importing the generateRandomPassword function from import-users/route.ts

// POST handler to confirm user import
export async function POST(request: Request) {
  try {
    if (!supabaseAdmin) {
      console.error(
        "[API POST] Supabase admin client is not available due to initialization error.",
      );
      return NextResponse.json(
        { error: "Server configuration error." },
        { status: 500 },
      );
    }

    // Define interface for user data
    interface UserImportData {
      email: string;
      firstName: string;
      lastName: string;
      identityDocument: string;
      status: "valid" | "invalid" | "duplicate";
      errors?: string[];
    }

    // Get users to import from request
    const { users } = (await request.json()) as { users: UserImportData[] };

    if (!users || !Array.isArray(users) || users.length === 0) {
      return NextResponse.json(
        { error: "No valid users provided" },
        { status: 400 },
      );
    }

    // Filter only valid users
    const validUsers = users.filter((user) => user.status === "valid");

    if (validUsers.length === 0) {
      return NextResponse.json(
        { error: "No valid users to import" },
        { status: 400 },
      );
    }

    // Import users
    const results = {
      total: validUsers.length,
      imported: 0,
      failed: 0,
      errors: [] as { email: string; error: string }[],
    };

    for (const user of validUsers) {
      try {
        // Generate a random password for the user
        const password = generateRandomPassword(12);

        // Create user in auth.users
        const { data: userData, error: createError } =
          await supabaseAdmin.auth.admin.createUser({
            email: user.email,
            password: password,
            email_confirm: true, // Auto-confirm email
            user_metadata: {
              role: "student",
              first_name: user.firstName,
              last_name: user.lastName,
              identity_document: user.identityDocument,
            },
          });

        if (createError) {
          throw createError;
        }

        // Update the corresponding public.users row
        const { error: profileError } = await supabaseAdmin
          .from("users")
          .upsert(
            {
              id: userData.user.id,
              email: user.email,
              role: "student",
              first_name: user.firstName,
              last_name: user.lastName,
              identity_document: user.identityDocument,
            },
            {
              onConflict: "id",
              ignoreDuplicates: false,
            },
          );

        if (profileError) {
          // If we can't update the profile, delete the auth user to avoid orphaned auth users
          await supabaseAdmin.auth.admin.deleteUser(userData.user.id);
          throw profileError;
        }

        results.imported++;
      } catch (error) {
        results.failed++;
        const errorMessage =
          error instanceof Error ? error.message : "Error desconocido";
        results.errors.push({
          email: user.email,
          error: errorMessage,
        });
        console.error(`[API POST] Error importing user ${user.email}:`, error);
      }
    }

    return NextResponse.json({
      success: true,
      ...results,
    });
  } catch (error) {
    console.error("[API POST] Error confirming user import:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred";
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
