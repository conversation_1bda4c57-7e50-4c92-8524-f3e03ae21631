/**
 * @fileoverview Unit tests for BaseRepository class
 *
 * Tests the core repository functionality including CRUD operations,
 * querying with filters, batch operations, and database adapter integration.
 */

import type { DatabaseAdapter } from "../../adapters/database";
import {
  BaseRepository,
  type QueryFilter,
  type QueryOptions,
} from "../base-repository";

// Mock database adapter
const mockDatabaseAdapter = {
  findAll: jest.fn(),
  findById: jest.fn(),
  findOne: jest.fn(),
  findMany: jest.fn(),
  create: jest.fn(),
  createMany: jest.fn(),
  update: jest.fn(),
  updateMany: jest.fn(),
  delete: jest.fn(),
  deleteMany: jest.fn(),
  count: jest.fn(),
  exists: jest.fn(),
} as unknown as DatabaseAdapter;

// Test entity interface
interface TestEntity {
  id: string;
  name: string;
  email: string;
  is_active: boolean;
  created_at: string;
}

// Test implementation of BaseRepository
class TestRepository extends BaseRepository<TestEntity> {
  protected tableName = "test_entities";

  constructor() {
    super(mockDatabaseAdapter);
  }
}

describe("BaseRepository", () => {
  let repository: TestRepository;

  beforeEach(() => {
    jest.clearAllMocks();
    repository = new TestRepository();
  });

  describe("findAll", () => {
    it("should retrieve all records", async () => {
      const mockEntities: TestEntity[] = [
        {
          id: "1",
          name: "Test 1",
          email: "<EMAIL>",
          is_active: true,
          created_at: "2024-01-01",
        },
        {
          id: "2",
          name: "Test 2",
          email: "<EMAIL>",
          is_active: true,
          created_at: "2024-01-02",
        },
      ];

      mockDatabaseAdapter.findAll = jest.fn().mockResolvedValue(mockEntities);

      const result = await repository.findAll();

      expect(result).toEqual(mockEntities);
      expect(mockDatabaseAdapter.findAll).toHaveBeenCalledWith(
        "test_entities",
        undefined,
      );
    });

    it("should retrieve all records with options", async () => {
      const options: QueryOptions = {
        orderBy: "created_at",
        orderDirection: "desc",
        limit: 10,
      };

      const mockEntities: TestEntity[] = [
        {
          id: "2",
          name: "Test 2",
          email: "<EMAIL>",
          is_active: true,
          created_at: "2024-01-02",
        },
        {
          id: "1",
          name: "Test 1",
          email: "<EMAIL>",
          is_active: true,
          created_at: "2024-01-01",
        },
      ];

      mockDatabaseAdapter.findAll = jest.fn().mockResolvedValue(mockEntities);

      const result = await repository.findAll(options);

      expect(result).toEqual(mockEntities);
      expect(mockDatabaseAdapter.findAll).toHaveBeenCalledWith(
        "test_entities",
        options,
      );
    });
  });

  describe("findById", () => {
    it("should retrieve a record by ID", async () => {
      const mockEntity: TestEntity = {
        id: "1",
        name: "Test Entity",
        email: "<EMAIL>",
        is_active: true,
        created_at: "2024-01-01",
      };

      mockDatabaseAdapter.findById = jest.fn().mockResolvedValue(mockEntity);

      const result = await repository.findById("1");

      expect(result).toEqual(mockEntity);
      expect(mockDatabaseAdapter.findById).toHaveBeenCalledWith(
        "test_entities",
        "1",
      );
    });

    it("should return null when record not found", async () => {
      mockDatabaseAdapter.findById = jest.fn().mockResolvedValue(null);

      const result = await repository.findById("nonexistent");

      expect(result).toBeNull();
      expect(mockDatabaseAdapter.findById).toHaveBeenCalledWith(
        "test_entities",
        "nonexistent",
      );
    });
  });

  describe("findOne", () => {
    it("should find one record with filters", async () => {
      const filters: QueryFilter[] = [
        { column: "email", operator: "eq", value: "<EMAIL>" },
      ];

      const mockEntity: TestEntity = {
        id: "1",
        name: "Test Entity",
        email: "<EMAIL>",
        is_active: true,
        created_at: "2024-01-01",
      };

      mockDatabaseAdapter.findOne = jest.fn().mockResolvedValue(mockEntity);

      const result = await repository.findOne(filters);

      expect(result).toEqual(mockEntity);
      expect(mockDatabaseAdapter.findOne).toHaveBeenCalledWith(
        "test_entities",
        filters,
      );
    });

    it("should return null when no record matches filters", async () => {
      const filters: QueryFilter[] = [
        { column: "email", operator: "eq", value: "<EMAIL>" },
      ];

      mockDatabaseAdapter.findOne = jest.fn().mockResolvedValue(null);

      const result = await repository.findOne(filters);

      expect(result).toBeNull();
    });
  });

  describe("findMany", () => {
    it("should find multiple records with filters", async () => {
      const filters: QueryFilter[] = [
        { column: "is_active", operator: "eq", value: true },
      ];

      const options: QueryOptions = {
        orderBy: "name",
        orderDirection: "asc",
        limit: 5,
      };

      const mockEntities: TestEntity[] = [
        {
          id: "1",
          name: "Test 1",
          email: "<EMAIL>",
          is_active: true,
          created_at: "2024-01-01",
        },
        {
          id: "2",
          name: "Test 2",
          email: "<EMAIL>",
          is_active: true,
          created_at: "2024-01-02",
        },
      ];

      mockDatabaseAdapter.findMany = jest.fn().mockResolvedValue(mockEntities);

      const result = await repository.findMany(filters, options);

      expect(result).toEqual(mockEntities);
      expect(mockDatabaseAdapter.findMany).toHaveBeenCalledWith(
        "test_entities",
        filters,
        options,
      );
    });

    it("should return empty array when no records match", async () => {
      const filters: QueryFilter[] = [
        { column: "is_active", operator: "eq", value: false },
      ];

      mockDatabaseAdapter.findMany = jest.fn().mockResolvedValue([]);

      const result = await repository.findMany(filters);

      expect(result).toEqual([]);
    });
  });

  describe("create", () => {
    it("should create a new record", async () => {
      const newEntity: Partial<TestEntity> = {
        name: "New Test",
        email: "<EMAIL>",
        is_active: true,
      };

      const createdEntity: TestEntity = {
        id: "new-id",
        ...newEntity,
        created_at: "2024-01-15",
      } as TestEntity;

      mockDatabaseAdapter.create = jest.fn().mockResolvedValue(createdEntity);

      const result = await repository.create(newEntity);

      expect(result).toEqual(createdEntity);
      expect(mockDatabaseAdapter.create).toHaveBeenCalledWith(
        "test_entities",
        newEntity,
      );
    });
  });

  describe("createMany", () => {
    it("should create multiple records", async () => {
      const newEntities: Partial<TestEntity>[] = [
        { name: "Test 1", email: "<EMAIL>", is_active: true },
        { name: "Test 2", email: "<EMAIL>", is_active: true },
      ];

      const createdEntities: TestEntity[] = [
        { id: "1", ...newEntities[0], created_at: "2024-01-15" } as TestEntity,
        { id: "2", ...newEntities[1], created_at: "2024-01-15" } as TestEntity,
      ];

      mockDatabaseAdapter.createMany = jest
        .fn()
        .mockResolvedValue(createdEntities);

      const result = await repository.createMany(newEntities);

      expect(result).toEqual(createdEntities);
      expect(mockDatabaseAdapter.createMany).toHaveBeenCalledWith(
        "test_entities",
        newEntities,
      );
    });
  });

  describe("update", () => {
    it("should update a record by ID", async () => {
      const updateData: Partial<TestEntity> = {
        name: "Updated Test",
        is_active: false,
      };

      const updatedEntity: TestEntity = {
        id: "1",
        name: "Updated Test",
        email: "<EMAIL>",
        is_active: false,
        created_at: "2024-01-01",
      };

      mockDatabaseAdapter.update = jest.fn().mockResolvedValue(updatedEntity);

      const result = await repository.update("1", updateData);

      expect(result).toEqual(updatedEntity);
      expect(mockDatabaseAdapter.update).toHaveBeenCalledWith(
        "test_entities",
        "1",
        updateData,
      );
    });

    it("should return null when record not found for update", async () => {
      const updateData: Partial<TestEntity> = {
        name: "Updated Test",
      };

      mockDatabaseAdapter.update = jest.fn().mockResolvedValue(null);

      const result = await repository.update("nonexistent", updateData);

      expect(result).toBeNull();
    });
  });

  describe("updateMany", () => {
    it("should update multiple records with filters", async () => {
      const filters: QueryFilter[] = [
        { column: "is_active", operator: "eq", value: true },
      ];

      const updateData: Partial<TestEntity> = {
        is_active: false,
      };

      const updatedEntities: TestEntity[] = [
        {
          id: "1",
          name: "Test 1",
          email: "<EMAIL>",
          is_active: false,
          created_at: "2024-01-01",
        },
        {
          id: "2",
          name: "Test 2",
          email: "<EMAIL>",
          is_active: false,
          created_at: "2024-01-02",
        },
      ];

      mockDatabaseAdapter.updateMany = jest
        .fn()
        .mockResolvedValue(updatedEntities);

      const result = await repository.updateMany(filters, updateData);

      expect(result).toEqual(updatedEntities);
      expect(mockDatabaseAdapter.updateMany).toHaveBeenCalledWith(
        "test_entities",
        filters,
        updateData,
      );
    });
  });

  describe("delete", () => {
    it("should delete a record by ID", async () => {
      mockDatabaseAdapter.delete = jest.fn().mockResolvedValue(true);

      const result = await repository.delete("1");

      expect(result).toBe(true);
      expect(mockDatabaseAdapter.delete).toHaveBeenCalledWith(
        "test_entities",
        "1",
      );
    });

    it("should return false when record not found for deletion", async () => {
      mockDatabaseAdapter.delete = jest.fn().mockResolvedValue(false);

      const result = await repository.delete("nonexistent");

      expect(result).toBe(false);
    });
  });

  describe("deleteMany", () => {
    it("should delete multiple records with filters", async () => {
      const filters: QueryFilter[] = [
        { column: "is_active", operator: "eq", value: false },
      ];

      mockDatabaseAdapter.deleteMany = jest.fn().mockResolvedValue(3);

      const result = await repository.deleteMany(filters);

      expect(result).toBe(3);
      expect(mockDatabaseAdapter.deleteMany).toHaveBeenCalledWith(
        "test_entities",
        filters,
      );
    });
  });

  describe("count", () => {
    it("should count all records", async () => {
      mockDatabaseAdapter.count = jest.fn().mockResolvedValue(10);

      const result = await repository.count();

      expect(result).toBe(10);
      expect(mockDatabaseAdapter.count).toHaveBeenCalledWith(
        "test_entities",
        undefined,
      );
    });

    it("should count records with filters", async () => {
      const filters: QueryFilter[] = [
        { column: "is_active", operator: "eq", value: true },
      ];

      mockDatabaseAdapter.count = jest.fn().mockResolvedValue(7);

      const result = await repository.count(filters);

      expect(result).toBe(7);
      expect(mockDatabaseAdapter.count).toHaveBeenCalledWith(
        "test_entities",
        filters,
      );
    });
  });

  describe("exists", () => {
    it("should return true when record exists", async () => {
      mockDatabaseAdapter.exists = jest.fn().mockResolvedValue(true);

      const result = await repository.exists("1");

      expect(result).toBe(true);
      expect(mockDatabaseAdapter.exists).toHaveBeenCalledWith(
        "test_entities",
        "1",
      );
    });

    it("should return false when record does not exist", async () => {
      mockDatabaseAdapter.exists = jest.fn().mockResolvedValue(false);

      const result = await repository.exists("nonexistent");

      expect(result).toBe(false);
    });
  });

  describe("error handling", () => {
    it("should propagate database adapter errors", async () => {
      const error = new Error("Database connection failed");
      mockDatabaseAdapter.findAll = jest.fn().mockRejectedValue(error);

      await expect(repository.findAll()).rejects.toThrow(
        "Database connection failed",
      );
    });
  });
});
