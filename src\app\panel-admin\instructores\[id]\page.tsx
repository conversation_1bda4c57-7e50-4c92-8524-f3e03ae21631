"use client";

import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { ArrowLeft, Edit, Loader2, Mail, Phone } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

// Define types for Instructor
type Instructor = {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  signature_url: string | null;
  created_at: string;
  updated_at: string;
};

export default function InstructorDetails() {
  const supabase = createClientComponentClient();
  const router = useRouter();
  const params = useParams();
  const instructorId = params.id as string;

  // State
  const [instructor, setInstructor] = useState<Instructor | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Cargar datos del instructor
  useEffect(() => {
    async function loadInstructor() {
      try {
        setLoading(true);

        const { data, error } = await supabase
          .from("instructors")
          .select("*")
          .eq("id", instructorId)
          .single();

        if (error) throw error;

        if (!data) throw new Error("Instructor no encontrado");

        setInstructor(data);
      } catch (error: any) {
        console.error("Error loading instructor:", error);
        setError(error.message || "Error al cargar los datos del instructor");
      } finally {
        setLoading(false);
      }
    }

    loadInstructor();
  }, [supabase, instructorId]);

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Detalles del Instructor</h1>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push("/panel-admin/instructores")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Volver
          </Button>
          <Link href={`/panel-admin/instructores/editar/${instructorId}`}>
            <Button>
              <Edit className="mr-2 h-4 w-4" />
              Editar
            </Button>
          </Link>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : instructor ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Información del Instructor</CardTitle>
              <CardDescription>Datos básicos del instructor</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="h-16 w-16 rounded-full bg-indigo-100 flex items-center justify-center mr-4">
                    <span className="text-indigo-800 font-bold text-2xl">
                      {instructor.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">{instructor.name}</h3>
                    <p className="text-gray-500">Instructor</p>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="text-sm font-medium text-gray-500 mb-2">
                    Información de contacto
                  </h4>

                  {instructor.email && (
                    <div className="flex items-center mb-2">
                      <Mail className="h-4 w-4 text-gray-400 mr-2" />
                      <span>{instructor.email}</span>
                    </div>
                  )}

                  {instructor.phone && (
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 text-gray-400 mr-2" />
                      <span>{instructor.phone}</span>
                    </div>
                  )}

                  {!instructor.email && !instructor.phone && (
                    <p className="text-gray-500">
                      No hay información de contacto disponible
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Firma del Instructor</CardTitle>
              <CardDescription>
                Firma utilizada en los certificados
              </CardDescription>
            </CardHeader>
            <CardContent>
              {instructor.signature_url ? (
                <div className="border rounded-md p-4">
                  <div className="relative h-32 w-full mb-2">
                    <Image
                      src={instructor.signature_url}
                      alt="Firma del instructor"
                      fill
                      style={{ objectFit: "contain" }}
                    />
                  </div>
                  <p className="text-center text-sm text-gray-500 mt-2">
                    Esta firma se mostrará en los certificados emitidos por este
                    instructor
                  </p>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center border border-dashed rounded-md p-6">
                  <p className="text-gray-500 mb-4">No hay firma disponible</p>
                  <Link
                    href={`/panel-admin/instructores/editar/${instructorId}`}
                  >
                    <Button variant="outline" size="sm">
                      Añadir firma
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500">No se encontró el instructor</p>
        </div>
      )}
    </div>
  );
}
