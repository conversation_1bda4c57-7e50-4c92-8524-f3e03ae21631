# Guía de Migración - Sistema de Temas Moderno QR CURSE

## 📋 Descripción General

Esta guía te ayudará a migrar de los componentes tradicionales a los nuevos componentes modernos con efectos 2025.

## 🔄 Migraciones de Componentes

### Card → ModernCard

**Antes:**
```tsx
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';

<Card className="hover:shadow-md">
  <CardHeader>
    <CardTitle>Título</CardTitle>
  </CardHeader>
  <CardContent>
    Contenido
  </CardContent>
</Card>
```

**Después:**
```tsx
import { ModernCard, ModernCardHeader, ModernCardTitle, ModernCardContent } from '@/components/ui/modern-card';

<ModernCard variant="glass" interactive glowEffect>
  <ModernCardHeader>
    <ModernCardTitle>Título</ModernCardTitle>
  </ModernCardHeader>
  <ModernCardContent>
    Contenido
  </ModernCardContent>
</ModernCard>
```

### Button → ModernButton

**Antes:**
```tsx
import { Button } from '@/components/ui/button';

<Button variant="default" size="lg" disabled={loading}>
  {loading ? "Cargando..." : "Guardar"}
</Button>
```

**Después:**
```tsx
import { ModernButton } from '@/components/ui/modern-button';

<ModernButton 
  variant="gradient" 
  size="lg" 
  loading={loading}
  loadingText="Guardando..."
  ripple
>
  Guardar
</ModernButton>
```

### Input → ModernInput

**Antes:**
```tsx
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

<div>
  <Label htmlFor="email">Email</Label>
  <Input 
    id="email"
    type="email" 
    placeholder="<EMAIL>"
  />
</div>
```

**Después:**
```tsx
import { ModernInput } from '@/components/ui/modern-input';

<ModernInput
  variant="floating"
  label="Email"
  type="email"
  placeholder="<EMAIL>"
  leftIcon={Mail}
  clearable
/>
```

## 🎨 Actualización de Estilos

### Clases CSS Obsoletas → Nuevas

| Antes | Después | Descripción |
|-------|---------|-------------|
| `hover:shadow-lg` | `card-modern` | Tarjeta con efectos modernos |
| `bg-white border` | `glass` | Efecto glassmorphism |
| `shadow-inner` | `neuro-inset` | Efecto neumorphism hundido |
| `transform hover:scale-105` | `interactive` | Micro-interacciones |
| `animate-pulse` | `animate-pulse-subtle` | Animación sutil |

### Variables CSS Actualizadas

**Antes:**
```css
:root {
  --primary: #3b82f6;
  --background: #ffffff;
}

.dark {
  --primary: #60a5fa;
  --background: #0f172a;
}
```

**Después:**
```css
:root {
  --primary: 210 100% 20%;
  --background: 0 0% 100%;
  --glass-background: 0 0% 100% / 0.8;
}

.dark {
  --primary: 217 91% 60%;
  --background: 224 71% 4%;
  --glass-background: 224 71% 4% / 0.8;
}
```

## 🔧 Configuración de Tailwind

### Actualizar tailwind.config.js

```js
// Agregar estas extensiones
module.exports = {
  theme: {
    extend: {
      // Nuevas animaciones
      animation: {
        'pulse-subtle': 'pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'slide-in-right': 'slide-in-right 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        'fade-in-up': 'fade-in-up 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      },
      
      // Colores glassmorphism
      colors: {
        glass: {
          background: "hsl(var(--glass-background))",
          border: "hsl(var(--glass-border))",
        },
      },
      
      // Backdrop blur
      backdropBlur: {
        'glass': 'var(--glass-backdrop-blur)',
      },
    },
  },
};
```

## 📱 Migración Responsive

### Grid System

**Antes:**
```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
  {items.map(item => <div key={item.id}>{item.name}</div>)}
</div>
```

**Después:**
```tsx
import { ResponsiveGrid } from '@/components/ui/responsive';

<ResponsiveGrid
  columns={{ xs: 1, md: 2, lg: 4 }}
  gap="md"
>
  {items.map(item => <div key={item.id}>{item.name}</div>)}
</ResponsiveGrid>
```

### Breakpoint Detection

**Antes:**
```tsx
const [isMobile, setIsMobile] = useState(false);

useEffect(() => {
  const checkMobile = () => {
    setIsMobile(window.innerWidth < 768);
  };
  
  checkMobile();
  window.addEventListener('resize', checkMobile);
  return () => window.removeEventListener('resize', checkMobile);
}, []);
```

**Después:**
```tsx
import { useBreakpoint } from '@/components/ui/responsive';

const { isMobile, isTablet, isDesktop } = useBreakpoint();
```

## ♿ Migración de Accesibilidad

### Botones Accesibles

**Antes:**
```tsx
<button 
  className="btn"
  disabled={loading}
  aria-label="Guardar cambios"
>
  {loading ? "Cargando..." : "Guardar"}
</button>
```

**Después:**
```tsx
import { AccessibleButton } from '@/components/ui/accessibility';

<AccessibleButton
  variant="primary"
  loading={loading}
  loadingText="Guardando cambios..."
>
  Guardar
</AccessibleButton>
```

### Skip Links

**Agregar al layout principal:**
```tsx
import { SkipLink } from '@/components/ui/accessibility';

function Layout({ children }) {
  return (
    <>
      <SkipLink href="#main-content">
        Saltar al contenido principal
      </SkipLink>
      
      <header>...</header>
      
      <main id="main-content">
        {children}
      </main>
    </>
  );
}
```

## ✨ Migración de Animaciones

### Animaciones Básicas

**Antes:**
```tsx
<div className="opacity-0 animate-fade-in">
  Contenido
</div>
```

**Después:**
```tsx
import { AnimateIn } from '@/components/ui/animations';

<AnimateIn animation="fade" duration="normal" delay={200}>
  <div>Contenido</div>
</AnimateIn>
```

### Listas Animadas

**Antes:**
```tsx
{items.map((item, index) => (
  <div 
    key={item.id}
    className="animate-fade-in"
    style={{ animationDelay: `${index * 100}ms` }}
  >
    {item.name}
  </div>
))}
```

**Después:**
```tsx
import { StaggeredList } from '@/components/ui/animations';

<StaggeredList delay={100}>
  {items.map(item => (
    <div key={item.id}>{item.name}</div>
  ))}
</StaggeredList>
```

## 🎯 Migración del Sidebar

### Sidebar Básico → Moderno

**Antes:**
```tsx
<aside className="w-64 bg-white border-r">
  <nav>
    {navItems.map(item => (
      <Link 
        key={item.href}
        href={item.href}
        className="flex items-center px-4 py-2 hover:bg-gray-100"
      >
        <item.icon className="w-5 h-5 mr-3" />
        {item.label}
      </Link>
    ))}
  </nav>
</aside>
```

**Después:**
```tsx
import { DashboardSidebar, useSidebarState } from '@/components/dashboard/dashboard-sidebar';

function Layout() {
  const sidebar = useSidebarState();
  
  return (
    <DashboardSidebar
      isOpen={sidebar.isOpen}
      isCollapsed={sidebar.isCollapsed}
      onClose={sidebar.close}
      onToggleCollapse={sidebar.toggleCollapse}
      userRole="admin"
    />
  );
}
```

## 🔍 Checklist de Migración

### ✅ Componentes
- [ ] Migrar Card → ModernCard
- [ ] Migrar Button → ModernButton  
- [ ] Migrar Input → ModernInput
- [ ] Actualizar Sidebar
- [ ] Implementar componentes responsive

### ✅ Estilos
- [ ] Actualizar variables CSS
- [ ] Agregar clases utilitarias modernas
- [ ] Configurar Tailwind extensions
- [ ] Implementar efectos glassmorphism/neumorphism

### ✅ Animaciones
- [ ] Migrar animaciones básicas
- [ ] Implementar micro-interacciones
- [ ] Agregar animaciones de scroll
- [ ] Configurar listas escalonadas

### ✅ Accesibilidad
- [ ] Agregar skip links
- [ ] Migrar a botones accesibles
- [ ] Implementar gestión de foco
- [ ] Verificar contraste WCAG 2.1 AA

### ✅ Responsive
- [ ] Migrar grid system
- [ ] Implementar breakpoint hooks
- [ ] Actualizar componentes responsive
- [ ] Testing en múltiples dispositivos

## 🚨 Problemas Comunes

### Error: "glass class not found"
**Solución:** Asegúrate de importar el archivo CSS de temas:
```tsx
// En tu layout principal o _app.tsx
import '@/styles/themes/enhanced-theme.css';
```

### Error: "Animation not working"
**Solución:** Verifica que las keyframes estén definidas en CSS:
```css
/* Debe estar en enhanced-theme.css */
@keyframes fade-in-up {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
```

### Error: "Responsive hooks not working"
**Solución:** Asegúrate de usar los hooks solo en componentes cliente:
```tsx
"use client"; // Agregar al inicio del archivo

import { useBreakpoint } from '@/components/ui/responsive';
```

## 📞 Soporte

Si encuentras problemas durante la migración:

1. Revisa la documentación completa en `docs/ui/modern-theme-system.md`
2. Verifica los ejemplos en la sección de ejemplos prácticos
3. Asegúrate de que todas las dependencias estén actualizadas
4. Testa en múltiples navegadores y dispositivos
