# Database Schema Overview

## Schemas

| Schema Name | Description | Table Count |
|-------------|-------------|-------------|
| public | Main application data | 24 |
| auth | Authentication and user management | 16 |
| storage | File storage | 5 |
| realtime | Real-time subscriptions | 3 |
| supabase_migrations | Database migrations | 2 |
| vault | Secure data storage | 1 |
| extensions | PostgreSQL extensions | 0 |
| graphql | GraphQL API | 0 |
| graphql_public | Public GraphQL API | 0 |

## Main Tables in Public Schema

| Table Name | Description | Row Count | Column Count |
|------------|-------------|-----------|--------------|
| users | User profiles and information | 7 | 11 |
| certificates | Issued certificates and diplomas | 2 | 12 |
| certificate_templates | Templates for certificates | 1 | 10 |
| companies | Company information | 1 | 11 |
| courses | Course information | 1 | 10 |
| grades | Student grades | 0 | 7 |
| notifications | System notifications | 0 | 16 |
| contacts | Contact information | 0 | 5 |
| enrollments | Course enrollments | 0 | 6 |
| modules | Course modules | 0 | 7 |
| lessons | Module lessons | 0 | 7 |
| attendance | Student attendance records | 0 | 8 |
| assessments | Course assessments | 0 | 8 |

## Entity Relationship Diagram (ERD)

```
users
  ↑↓ 1:N
courses ← enrollments
  ↓ 1:N       ↑ N:1
modules       users
  ↓ 1:N
lessons
  ↓ 1:N
attendance ← users

certificates
  ↑ N:1    ↑ N:1
users      courses

companies
  ↑ 1:N
users
```

## Custom Types

The database uses several custom enumerated types:

- **user_role**: admin, instructor, student, company_rep
- **course_status**: draft, published, archived
- **enrollment_status**: active, completed, dropped
- **attendance_status**: present, absent, excused
- **assessment_type**: quiz, exam, assignment
- **certificate_template_type**: diploma, certificate, attendance
- **quotation_status**: pending, approved, rejected
- **document_type**: id, company_card, other

## Security Model

The database implements Row Level Security (RLS) policies to control access to data based on user roles and ownership. Key security functions include:

- **is_admin()**: Checks if the current user has admin role
- **is_instructor()**: Checks if the current user has instructor role
- **is_own_profile()**: Checks if the current user is accessing their own profile

See the [Policies](./policies.md) documentation for detailed information on RLS policies.
