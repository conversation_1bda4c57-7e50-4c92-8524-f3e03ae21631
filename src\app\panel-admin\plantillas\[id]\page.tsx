"use client";

import { use<PERSON>arams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import SignatureUploader from "@/components/certificates/SignatureUploader";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/lib/supabase";

export default function EditarPlantillaPage() {
  const params = useParams();
  const router = useRouter();
  const templateId = params.id as string;

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [template, setTemplate] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    template_type: "certificate",
    html_template: "",
    css_styles: "",
    is_default: false,
    signature_url: "",
  });

  useEffect(() => {
    async function fetchTemplate() {
      try {
        setLoading(true);
        setError(null);

        const { data, error } = await supabase
          .from("certificate_templates")
          .select("*")
          .eq("id", templateId)
          .single();

        if (error) throw error;

        setTemplate(data);
        setFormData({
          name: data.name || "",
          description: data.description || "",
          template_type: data.template_type || "certificate",
          html_template: data.html_template || "",
          css_styles: data.css_styles || "",
          is_default: data.is_default || false,
          signature_url: data.signature_url || "",
        });
      } catch (error: any) {
        console.error("Error fetching template:", error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    }

    if (templateId) {
      fetchTemplate();
    }
  }, [templateId]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);

      const { error } = await supabase
        .from("certificate_templates")
        .update(formData)
        .eq("id", templateId);

      if (error) throw error;

      toast({
        title: "Plantilla actualizada",
        description: "Los cambios se han guardado correctamente.",
      });

      // Refresh the page data
      router.refresh();
    } catch (error: any) {
      console.error("Error updating template:", error);
      toast({
        title: "Error",
        description: `No se pudo actualizar la plantilla. ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (
      !confirm(
        "¿Estás seguro de que deseas eliminar esta plantilla? Esta acción no se puede deshacer.",
      )
    ) {
      return;
    }

    try {
      setSaving(true);

      const { error } = await supabase
        .from("certificate_templates")
        .delete()
        .eq("id", templateId);

      if (error) throw error;

      toast({
        title: "Plantilla eliminada",
        description: "La plantilla se ha eliminado correctamente.",
      });

      router.push("/panel-admin/plantillas");
    } catch (error: any) {
      console.error("Error deleting template:", error);
      toast({
        title: "Error",
        description: `No se pudo eliminar la plantilla. ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <Skeleton className="h-10 w-1/3 mb-6" />
        <Tabs defaultValue="info">
          <TabsList className="mb-4">
            <Skeleton className="h-10 w-24 mr-2" />
            <Skeleton className="h-10 w-24 mr-2" />
            <Skeleton className="h-10 w-24 mr-2" />
            <Skeleton className="h-10 w-24" />
          </TabsList>
          <Skeleton className="h-96 w-full" />
        </Tabs>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
          <h3 className="text-lg font-medium">Error al cargar la plantilla</h3>
          <p>{error}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => router.push("/panel-admin/plantillas")}
          >
            Volver a la lista de plantillas
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">{template.name}</h1>
          <div className="flex items-center mt-1">
            <span className="text-sm text-gray-500 mr-2 capitalize">
              Tipo: {template.template_type}
            </span>
            {template.is_default && (
              <Badge className="bg-blue-500">Predeterminada</Badge>
            )}
          </div>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push("/panel-admin/plantillas")}
        >
          Volver a la lista
        </Button>
      </div>

      <form onSubmit={handleSubmit}>
        <Tabs defaultValue="info">
          <TabsList className="mb-4">
            <TabsTrigger value="info">Información Básica</TabsTrigger>
            <TabsTrigger value="html">Plantilla HTML</TabsTrigger>
            <TabsTrigger value="css">Estilos CSS</TabsTrigger>
            <TabsTrigger value="preview">Vista Previa</TabsTrigger>
          </TabsList>

          <TabsContent value="info">
            <Card>
              <CardHeader>
                <CardTitle>Información de la Plantilla</CardTitle>
                <CardDescription>
                  Datos básicos de la plantilla de certificado
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nombre de la Plantilla</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Descripción</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="template_type">Tipo de Plantilla</Label>
                  <Select
                    value={formData.template_type}
                    onValueChange={(value) =>
                      handleSelectChange("template_type", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona un tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="certificate">Certificado</SelectItem>
                      <SelectItem value="diploma">Diploma</SelectItem>
                      <SelectItem value="attendance">Asistencia</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2 pt-4">
                  <Switch
                    id="is_default"
                    checked={formData.is_default}
                    onCheckedChange={(checked) =>
                      handleSwitchChange("is_default", checked)
                    }
                  />
                  <Label htmlFor="is_default">
                    Establecer como plantilla predeterminada
                  </Label>
                </div>

                <div className="pt-6 border-t mt-6">
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-4">
                      Firma para el Certificado
                    </h3>
                    <SignatureUploader
                      templateId={templateId}
                      currentSignatureUrl={formData.signature_url}
                      onSignatureUploaded={(url) => {
                        setFormData((prev) => ({
                          ...prev,
                          signature_url: url,
                        }));
                      }}
                    />
                    <p className="text-sm text-gray-500 mt-2">
                      Esta firma se mostrará en los certificados generados con
                      esta plantilla.
                    </p>
                  </div>

                  <Button
                    type="button"
                    variant="destructive"
                    onClick={handleDelete}
                    disabled={saving}
                  >
                    Eliminar Plantilla
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="html">
            <Card>
              <CardHeader>
                <CardTitle>Plantilla HTML</CardTitle>
                <CardDescription>
                  Estructura HTML del certificado. Usa variables como{" "}
                  {"{{participantName}}"}, {"{{courseTitle}}"}, etc.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  id="html_template"
                  name="html_template"
                  value={formData.html_template}
                  onChange={handleChange}
                  rows={20}
                  className="font-mono text-sm"
                />

                <div className="mt-4 bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium mb-2">Variables disponibles:</h3>
                  <ul className="list-disc pl-5 space-y-1 text-sm">
                    <li>
                      <code>{"{{participantName}}"}</code> - Nombre del
                      estudiante
                    </li>
                    <li>
                      <code>{"{{courseTitle}}"}</code> - Título del curso
                    </li>
                    <li>
                      <code>{"{{certificateNumber}}"}</code> - Número de
                      certificado
                    </li>
                    <li>
                      <code>{"{{completionDate}}"}</code> - Fecha de
                      finalización
                    </li>
                    <li>
                      <code>{"{{duration}}"}</code> - Duración del curso
                    </li>
                    <li>
                      <code>{"{{instructorName}}"}</code> - Nombre del
                      instructor
                    </li>
                    <li>
                      <code>{"{{issuingAuthority}}"}</code> - Autoridad emisora
                    </li>
                    <li>
                      <code>{"{{logo}}"}</code> - Logo de la institución
                    </li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="css">
            <Card>
              <CardHeader>
                <CardTitle>Estilos CSS</CardTitle>
                <CardDescription>
                  Estilos CSS para personalizar la apariencia del certificado
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  id="css_styles"
                  name="css_styles"
                  value={formData.css_styles}
                  onChange={handleChange}
                  rows={20}
                  className="font-mono text-sm"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preview">
            <Card>
              <CardHeader>
                <CardTitle>Vista Previa</CardTitle>
                <CardDescription>
                  Previsualización de cómo se verá el certificado
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-100 p-4 rounded-md">
                  <div className="bg-white border rounded-md overflow-hidden">
                    <iframe
                      src={`/certificado-preview/${templateId}`}
                      className="w-full border-0"
                      style={{ height: "500px", overflow: "hidden" }}
                      title="Vista previa del certificado"
                      scrolling="no"
                    />
                  </div>

                  <div className="flex justify-center mt-4">
                    <Button
                      variant="outline"
                      onClick={() =>
                        window.open(
                          `/certificado-preview/${templateId}`,
                          "_blank",
                        )
                      }
                    >
                      Abrir en Nueva Ventana
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end mt-6">
          <Button type="submit" disabled={saving}>
            {saving ? "Guardando..." : "Guardar Cambios"}
          </Button>
        </div>
      </form>
    </div>
  );
}
