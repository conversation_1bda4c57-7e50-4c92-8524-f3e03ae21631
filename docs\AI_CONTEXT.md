# 🤖 AI Context & Automation System

> **See Also:** [INDEX.md](./INDEX.md) | [PLANNING.md](./PLANNING.md) | [RULES.md](./RULES.md)

This document describes how the QR Course Scaffold leverages AI agents, bots, and automated context generation to stay always up-to-date, maintainable, and LLM/AI-friendly.

---

## 1. Overview
- **Goal:** Provide a living, structured, and semantic context for both human developers and AI coding agents (LLMs, Copilot, Jules, coderabbitai, etc.).
- **How:** Automate extraction of project metadata and embeddings, orchestrate bots for code, dependency, and documentation updates, and expose all context in accessible formats.

---

## 2. Bot Orchestration

### **Jules (Google Labs Jules)**
- Automates code changes, refactors, and documentation from issues labeled `jules`.
- Follows clear, scoped prompts (see [Jules docs](https://jules.google/docs/running-tasks/#starting-tasks-from-github-issues)).
- Creates branches, PRs, and summaries for each task.

### **coderabbitai**
- Provides code suggestions, refactoring, and documentation in PRs and issues.
- Can be prompted for code reviews, best practices, and documentation generation.

### **Renovate & Dependabot**
- Detect and open PRs for dependency updates.
- CI/CD runs tests and merges safe updates automatically.

### **Vercel, Google Cloud Build**
- Deploy previews and production on PRs and main branch.
- Status checks and notifications in PRs.

---

## 3. Metadata & Embedding Generation

### **What is generated?**
- **project_metadata.json**: Structure, modules, components, endpoints, tables, RLS policies, and documentation snippets.
- **project_vectors.jsonl**: Embeddings (vector representations) of all docs, comments, and module descriptions for semantic search and LLM context.

### **How is it generated?**
- **Scripts in `.github/scripts/`**:
  - `extract_metadata.js`: Recursively scans the project, extracts structure, comments, and documentation.
  - `generate_embeddings.js`: Uses HuggingFace or OpenAI API to vectorize extracted texts.
- **GitHub Actions Workflow**:
  - Runs on push to main or on schedule.
  - Commits updated metadata and embeddings to the repo.

### **Example Workflow**
```yaml
name: Generate Project Metadata & Embeddings
on:
  push:
    branches: [main]
  workflow_dispatch:
jobs:
  metadata:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Install dependencies
        run: npm install
      - name: Extract project metadata
        run: node .github/scripts/extract_metadata.js
      - name: Generate embeddings
        run: node .github/scripts/generate_embeddings.js
      - name: Commit and push metadata
        uses: EndBug/add-and-commit@v9
        with:
          add: 'project_metadata.json project_vectors.jsonl'
          message: 'chore: update project metadata and embeddings'
```

---

## 4. How to Use the Context (for Agents & Humans)
- **LLMs/Agents:**
  - Use `project_metadata.json` for codebase navigation, search, and context-aware code generation.
  - Use `project_vectors.jsonl` for semantic search, recommendations, and memory.
- **Developers:**
  - Search for modules, endpoints, or docs using the metadata file.
  - Use embeddings for advanced search or to power AI assistants.
- **Bots:**
  - Reference context files to generate more accurate PRs, documentation, and code changes.

---

## 5. Best Practices
- **Write clear, structured comments and documentation** in all modules/components.
- **Keep context files up-to-date** by running the extraction/generation scripts after major changes.
- **Label issues for Jules and coderabbitai** with clear, scoped prompts.
- **Review and prune dependencies regularly** to minimize context bloat.
- **Document all custom logic and architectural decisions** in `docs/`.

---

## 6. Example: Issue for Jules
```
Title: Refactor certificate validation logic for new Supabase API
Labels: jules

Prompt:
- Update the certificate validation logic to use the new Supabase edge function.
- Add tests for the new flow.
- Document the changes in the relevant module.
```

---

## 7. References
- [ComoCocinar: AI/LLM Context System Example](https://github.com/iberi22/ComoCocinar)
- [Jules Docs](https://jules.google/docs/running-tasks/#starting-tasks-from-github-issues)
- [Context Engineering Template](https://github.com/iberi22/context-engineering-template)

---

## Modernization Roadmap Reference

- See the [README](../README.md#-plan-de-modernización-y-mejora-continua-faseshitos) for the full phased plan.
- This AI context system is Phase 4 of the modernization roadmap.
- All bot orchestration and context automation is cross-referenced in [PLANNING.md](./PLANNING.md), [INDEX.md](./INDEX.md), and [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md).

---

> This system ensures the scaffold is always ready for both human and AI contributors, with living, semantic context and automation at its core.