# Dashboard Design Patterns

Patrones de diseño específicos para los dashboards de QR CURSE, incluyendo layouts responsivos, componentes de datos y patrones de interacción.

## Tabla de Contenidos

- [Arquitectura de Dashboard](#arquitectura-de-dashboard)
- [Patrones de Layout](#patrones-de-layout)
- [Componentes de Datos](#componentes-de-datos)
- [Patrones de Navegación](#patrones-de-navegación)
- [Estados de Carga y Error](#estados-de-carga-y-error)
- [Responsive Design](#responsive-design)
- [Accesibilidad](#accesibilidad)

## Arquitectura de Dashboard

### Estructura Base del Dashboard

```tsx
// Layout base para todos los dashboards
export default function DashboardLayout({
  children,
  title,
  subtitle,
  actions,
}: {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
  actions?: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-background">
      {/* Header con navegación */}
      <DashboardHeader />

      <div className="flex">
        {/* Sidebar responsivo */}
        <DashboardSidebar />

        {/* Contenido principal */}
        <main className="flex-1 p-6 lg:p-8">
          {/* Breadcrumbs */}
          <DashboardBreadcrumbs />

          {/* Header de página */}
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
                {subtitle && (
                  <p className="text-muted-foreground mt-1">{subtitle}</p>
                )}
              </div>
              {actions && (
                <div className="mt-4 sm:mt-0 flex gap-2">
                  {actions}
                </div>
              )}
            </div>
          </div>

          {/* Contenido de la página */}
          {children}
        </main>
      </div>
    </div>
  );
}
```

### Dashboard Header Component

```tsx
import { Bell, Search, User } from 'lucide-react';
import { ThemeToggle } from '@/components/theme/theme-toggle';

export function DashboardHeader() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        {/* Logo y título */}
        <div className="mr-4 flex">
          <Link href="/dashboard" className="mr-6 flex items-center space-x-2">
            <span className="font-bold">QR CURSE</span>
          </Link>
        </div>

        {/* Navegación principal */}
        <nav className="flex items-center space-x-6 text-sm font-medium">
          <Link href="/dashboard">Dashboard</Link>
          <Link href="/certificates">Certificados</Link>
          <Link href="/users">Usuarios</Link>
        </nav>

        <div className="flex flex-1 items-center justify-end space-x-2">
          {/* Búsqueda global */}
          <div className="w-full flex-1 md:w-auto md:flex-none">
            <Button variant="outline" className="relative w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64">
              <Search className="mr-2 h-4 w-4" />
              Buscar...
            </Button>
          </div>

          {/* Notificaciones */}
          <Button variant="ghost" size="sm">
            <Bell className="h-4 w-4" />
          </Button>

          {/* Toggle de tema */}
          <ThemeToggle />

          {/* Menú de usuario */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <User className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>Perfil</DropdownMenuItem>
              <DropdownMenuItem>Configuración</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>Cerrar Sesión</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
```

## Patrones de Layout

### Grid de Métricas

```tsx
export function MetricsGrid({ metrics }: { metrics: Metric[] }) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric) => (
        <Card key={metric.id}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {metric.title}
            </CardTitle>
            <metric.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metric.value}</div>
            <p className="text-xs text-muted-foreground">
              {metric.change > 0 ? '+' : ''}{metric.change}% desde el mes pasado
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
```

### Layout de Dos Columnas

```tsx
export function TwoColumnLayout({
  leftColumn,
  rightColumn,
  leftWidth = "2/3",
}: {
  leftColumn: React.ReactNode;
  rightColumn: React.ReactNode;
  leftWidth?: "1/2" | "2/3" | "3/4";
}) {
  const leftClass = {
    "1/2": "lg:w-1/2",
    "2/3": "lg:w-2/3",
    "3/4": "lg:w-3/4",
  }[leftWidth];

  const rightClass = {
    "1/2": "lg:w-1/2",
    "2/3": "lg:w-1/3",
    "3/4": "lg:w-1/4",
  }[leftWidth];

  return (
    <div className="flex flex-col lg:flex-row gap-6">
      <div className={leftClass}>
        {leftColumn}
      </div>
      <div className={rightClass}>
        {rightColumn}
      </div>
    </div>
  );
}
```

## Componentes de Datos

### Tabla de Datos Avanzada

```tsx
import { DataTable } from '@/components/ui/data-table';
import { ColumnDef } from '@tanstack/react-table';

interface Certificate {
  id: string;
  number: string;
  studentName: string;
  courseName: string;
  issueDate: string;
  status: 'active' | 'revoked' | 'expired';
}

const columns: ColumnDef<Certificate>[] = [
  {
    accessorKey: "number",
    header: "Número",
    cell: ({ row }) => (
      <div className="font-mono text-sm">
        {row.getValue("number")}
      </div>
    ),
  },
  {
    accessorKey: "studentName",
    header: "Estudiante",
    cell: ({ row }) => (
      <div className="flex items-center">
        <Avatar className="h-8 w-8 mr-2">
          <AvatarFallback>
            {row.getValue<string>("studentName").split(' ').map(n => n[0]).join('')}
          </AvatarFallback>
        </Avatar>
        <span>{row.getValue("studentName")}</span>
      </div>
    ),
  },
  {
    accessorKey: "status",
    header: "Estado",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return (
        <Badge variant={
          status === 'active' ? 'default' :
          status === 'revoked' ? 'destructive' : 'secondary'
        }>
          {status === 'active' ? 'Activo' :
           status === 'revoked' ? 'Revocado' : 'Expirado'}
        </Badge>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem>Ver detalles</DropdownMenuItem>
          <DropdownMenuItem>Descargar PDF</DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem className="text-red-600">
            Revocar
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ),
  },
];

export function CertificatesTable({ data }: { data: Certificate[] }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Certificados Recientes</CardTitle>
        <CardDescription>
          Lista de certificados emitidos recientemente
        </CardDescription>
      </CardHeader>
      <CardContent>
        <DataTable
          columns={columns}
          data={data}
          searchKey="studentName"
          searchPlaceholder="Buscar por estudiante..."
        />
      </CardContent>
    </Card>
  );
}
```

### Gráfico de Estadísticas

```tsx
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';

export function StatisticsChart({ data }: { data: any[] }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Certificados por Mes</CardTitle>
        <CardDescription>
          Número de certificados emitidos en los últimos 6 meses
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="certificates" fill="hsl(var(--primary))" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
```

## Patrones de Navegación

### Sidebar Responsivo

```tsx
export function DashboardSidebar() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside className={cn(
        "fixed left-0 top-14 z-50 h-[calc(100vh-3.5rem)] w-64 transform border-r bg-background transition-transform lg:translate-x-0",
        isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
      )}>
        <div className="flex h-full flex-col">
          <nav className="flex-1 space-y-2 p-4">
            <SidebarItem href="/dashboard" icon={Home}>
              Dashboard
            </SidebarItem>
            <SidebarItem href="/certificates" icon={Award}>
              Certificados
            </SidebarItem>
            <SidebarItem href="/users" icon={Users}>
              Usuarios
            </SidebarItem>
            <SidebarItem href="/courses" icon={BookOpen}>
              Cursos
            </SidebarItem>
            <SidebarItem href="/reports" icon={BarChart3}>
              Reportes
            </SidebarItem>
          </nav>

          <div className="border-t p-4">
            <SidebarItem href="/settings" icon={Settings}>
              Configuración
            </SidebarItem>
          </div>
        </div>
      </aside>

      {/* Mobile menu button */}
      <Button
        variant="ghost"
        size="sm"
        className="fixed left-4 top-4 z-50 lg:hidden"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Menu className="h-4 w-4" />
      </Button>
    </>
  );
}

function SidebarItem({
  href,
  icon: Icon,
  children
}: {
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const isActive = pathname === href;

  return (
    <Link
      href={href}
      className={cn(
        "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors",
        isActive
          ? "bg-primary text-primary-foreground"
          : "hover:bg-accent hover:text-accent-foreground"
      )}
    >
      <Icon className="h-4 w-4" />
      {children}
    </Link>
  );
}
```

## Estados de Carga y Error

### Skeleton Loading

```tsx
export function DashboardSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header skeleton */}
      <div className="flex justify-between items-center">
        <div>
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-32 mt-2" />
        </div>
        <Skeleton className="h-10 w-32" />
      </div>

      {/* Metrics grid skeleton */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-4 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-3 w-32 mt-2" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Table skeleton */}
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

### Error Boundary

```tsx
export function DashboardErrorBoundary({
  error,
  reset
}: {
  error: Error;
  reset: () => void;
}) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
      <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
      <h2 className="text-xl font-semibold mb-2">
        Algo salió mal
      </h2>
      <p className="text-muted-foreground text-center mb-4 max-w-md">
        Ocurrió un error inesperado. Por favor, intenta recargar la página.
      </p>
      <Button onClick={reset}>
        Intentar de nuevo
      </Button>
    </div>
  );
}
```

## Responsive Design

### Breakpoints y Patrones

```tsx
// Patrones de responsive design para dashboards
const responsivePatterns = {
  // Grid adaptativo
  adaptiveGrid: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4",

  // Flex responsivo
  responsiveFlex: "flex flex-col sm:flex-row gap-4",

  // Padding responsivo
  responsivePadding: "p-4 md:p-6 lg:p-8",

  // Texto responsivo
  responsiveText: "text-sm md:text-base lg:text-lg",

  // Ocultar en móvil
  hiddenMobile: "hidden md:block",

  // Solo móvil
  mobileOnly: "block md:hidden",
};

// Hook para detectar tamaño de pantalla
export function useBreakpoint() {
  const [breakpoint, setBreakpoint] = useState<'sm' | 'md' | 'lg' | 'xl'>('sm');

  useEffect(() => {
    const updateBreakpoint = () => {
      if (window.innerWidth >= 1280) setBreakpoint('xl');
      else if (window.innerWidth >= 1024) setBreakpoint('lg');
      else if (window.innerWidth >= 768) setBreakpoint('md');
      else setBreakpoint('sm');
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return breakpoint;
}
```

## Accesibilidad

### Patrones de Accesibilidad

```tsx
// Componente accesible para métricas
export function AccessibleMetric({
  title,
  value,
  change,
  icon: Icon
}: MetricProps) {
  const changeText = change > 0 ? 'aumento' : 'disminución';
  const ariaLabel = `${title}: ${value}, ${Math.abs(change)}% de ${changeText} desde el mes pasado`;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {title}
        </CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" aria-hidden="true" />
      </CardHeader>
      <CardContent>
        <div
          className="text-2xl font-bold"
          aria-label={ariaLabel}
          role="status"
        >
          {value}
        </div>
        <p className="text-xs text-muted-foreground" aria-hidden="true">
          {change > 0 ? '+' : ''}{change}% desde el mes pasado
        </p>
      </CardContent>
    </Card>
  );
}

// Skip links para navegación por teclado
export function SkipLinks() {
  return (
    <div className="sr-only focus-within:not-sr-only">
      <a
        href="#main-content"
        className="fixed top-4 left-4 z-50 bg-primary text-primary-foreground px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
      >
        Saltar al contenido principal
      </a>
      <a
        href="#navigation"
        className="fixed top-4 left-32 z-50 bg-primary text-primary-foreground px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
      >
        Saltar a la navegación
      </a>
    </div>
  );
}
```

Estos patrones proporcionan una base sólida para crear dashboards consistentes, accesibles y responsivos en la plataforma QR CURSE.
