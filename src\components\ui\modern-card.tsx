/**
 * @fileoverview Modern Card Component (2025 UI/UX Trends)
 *
 * Componente de tarjeta moderna que implementa las tendencias UI/UX 2025:
 * - Glassmorphism
 * - Neumorphism
 * - Micro-interacciones
 * - Efectos de hover avanzados
 * - Diseño minimalista
 */

"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface ModernCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "glass" | "neuro" | "elevated" | "minimal";
  size?: "sm" | "md" | "lg" | "xl";
  interactive?: boolean;
  glowEffect?: boolean;
  children: React.ReactNode;
}

const cardVariants = {
  default: "bg-card text-card-foreground border border-border shadow-md",
  glass: "glass border border-glass-border backdrop-blur-glass",
  neuro: "neuro text-foreground",
  elevated: "bg-card text-card-foreground border border-border shadow-xl hover:shadow-2xl",
  minimal: "bg-transparent text-foreground border-none shadow-none",
};

const cardSizes = {
  sm: "p-4 rounded-lg",
  md: "p-6 rounded-xl",
  lg: "p-8 rounded-2xl",
  xl: "p-10 rounded-3xl",
};

export const ModernCard = React.forwardRef<HTMLDivElement, ModernCardProps>(
  ({ 
    className, 
    variant = "default", 
    size = "md", 
    interactive = false,
    glowEffect = false,
    children, 
    ...props 
  }, ref) => {
    const [isHovered, setIsHovered] = React.useState(false);

    return (
      <div
        ref={ref}
        className={cn(
          "transition-all duration-300 ease-in-out",
          cardVariants[variant],
          cardSizes[size],
          interactive && [
            "cursor-pointer hover:scale-[1.02] active:scale-[0.98]",
            "hover:-translate-y-1",
          ],
          glowEffect && isHovered && [
            "shadow-[0_0_30px_rgba(59,130,246,0.3)]",
            "dark:shadow-[0_0_30px_rgba(59,130,246,0.2)]",
          ],
          className
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        {...props}
      >
        {/* Efecto de brillo interno */}
        {glowEffect && (
          <div className={cn(
            "absolute inset-0 rounded-[inherit] opacity-0 transition-opacity duration-300",
            "bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5",
            isHovered && "opacity-100"
          )} />
        )}
        
        {/* Contenido */}
        <div className="relative z-10">
          {children}
        </div>
      </div>
    );
  }
);
ModernCard.displayName = "ModernCard";

/**
 * Header de la tarjeta moderna
 */
interface ModernCardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export const ModernCardHeader = React.forwardRef<HTMLDivElement, ModernCardHeaderProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("flex flex-col space-y-1.5 pb-4", className)}
      {...props}
    >
      {children}
    </div>
  )
);
ModernCardHeader.displayName = "ModernCardHeader";

/**
 * Título de la tarjeta moderna
 */
interface ModernCardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode;
  gradient?: boolean;
}

export const ModernCardTitle = React.forwardRef<HTMLParagraphElement, ModernCardTitleProps>(
  ({ className, children, gradient = false, ...props }, ref) => (
    <h3
      ref={ref}
      className={cn(
        "text-2xl font-semibold leading-none tracking-tight",
        gradient && [
          "bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent",
          "animate-pulse-subtle"
        ],
        className
      )}
      {...props}
    >
      {children}
    </h3>
  )
);
ModernCardTitle.displayName = "ModernCardTitle";

/**
 * Descripción de la tarjeta moderna
 */
interface ModernCardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode;
}

export const ModernCardDescription = React.forwardRef<HTMLParagraphElement, ModernCardDescriptionProps>(
  ({ className, children, ...props }, ref) => (
    <p
      ref={ref}
      className={cn("text-sm text-muted-foreground leading-relaxed", className)}
      {...props}
    >
      {children}
    </p>
  )
);
ModernCardDescription.displayName = "ModernCardDescription";

/**
 * Contenido de la tarjeta moderna
 */
interface ModernCardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export const ModernCardContent = React.forwardRef<HTMLDivElement, ModernCardContentProps>(
  ({ className, children, ...props }, ref) => (
    <div ref={ref} className={cn("pt-0", className)} {...props}>
      {children}
    </div>
  )
);
ModernCardContent.displayName = "ModernCardContent";

/**
 * Footer de la tarjeta moderna
 */
interface ModernCardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export const ModernCardFooter = React.forwardRef<HTMLDivElement, ModernCardFooterProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("flex items-center pt-4", className)}
      {...props}
    >
      {children}
    </div>
  )
);
ModernCardFooter.displayName = "ModernCardFooter";

/**
 * Tarjeta de estadística moderna
 */
interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon?: React.ComponentType<{ className?: string }>;
  trend?: "up" | "down" | "neutral";
  trendValue?: string;
  variant?: "default" | "glass" | "neuro";
}

export function StatCard({
  title,
  value,
  description,
  icon: Icon,
  trend = "neutral",
  trendValue,
  variant = "default",
}: StatCardProps) {
  const trendColors = {
    up: "text-success",
    down: "text-error",
    neutral: "text-muted-foreground",
  };

  return (
    <ModernCard variant={variant} interactive glowEffect>
      <ModernCardContent>
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-3xl font-bold">{value}</p>
            {description && (
              <p className="text-xs text-muted-foreground">{description}</p>
            )}
          </div>
          {Icon && (
            <div className="p-3 rounded-full bg-primary/10">
              <Icon className="h-6 w-6 text-primary" />
            </div>
          )}
        </div>
        {trendValue && (
          <div className={cn("flex items-center mt-2 text-sm", trendColors[trend])}>
            <span>{trendValue}</span>
          </div>
        )}
      </ModernCardContent>
    </ModernCard>
  );
}

/**
 * Tarjeta de acción moderna
 */
interface ActionCardProps {
  title: string;
  description: string;
  icon?: React.ComponentType<{ className?: string }>;
  action?: React.ReactNode;
  variant?: "default" | "glass" | "neuro";
}

export function ActionCard({
  title,
  description,
  icon: Icon,
  action,
  variant = "default",
}: ActionCardProps) {
  return (
    <ModernCard variant={variant} interactive>
      <ModernCardHeader>
        <div className="flex items-center space-x-3">
          {Icon && (
            <div className="p-2 rounded-lg bg-primary/10">
              <Icon className="h-5 w-5 text-primary" />
            </div>
          )}
          <ModernCardTitle className="text-lg">{title}</ModernCardTitle>
        </div>
      </ModernCardHeader>
      <ModernCardContent>
        <ModernCardDescription>{description}</ModernCardDescription>
      </ModernCardContent>
      {action && (
        <ModernCardFooter>
          {action}
        </ModernCardFooter>
      )}
    </ModernCard>
  );
}
