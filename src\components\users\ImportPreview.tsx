"use client";

import { <PERSON>ertCircle, CheckCircle, X } from "lucide-react";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";

interface UserData {
  email: string;
  firstName: string;
  lastName: string;
  identityDocument: string;
  status: "valid" | "invalid" | "duplicate";
  errors?: string[];
}

interface ImportPreviewProps {
  data: UserData[];
  onCancel: () => void;
  onConfirm: () => Promise<void>;
}

export function ImportPreview({
  data,
  onCancel,
  onConfirm,
}: ImportPreviewProps) {
  const [isImporting, setIsImporting] = useState(false);
  const [importError, setImportError] = useState<string | null>(null);

  const validUsers = data.filter((user) => user.status === "valid");
  const invalidUsers = data.filter((user) => user.status === "invalid");
  const duplicateUsers = data.filter((user) => user.status === "duplicate");

  const handleConfirm = async () => {
    if (validUsers.length === 0) {
      toast({
        title: "No hay usuarios válidos para importar",
        description: "Corrige los errores o sube un nuevo archivo.",
        variant: "destructive",
      });
      return;
    }

    setIsImporting(true);
    setImportError(null);

    try {
      await onConfirm();
      toast({
        title: "Importación exitosa",
        description: `Se importaron ${validUsers.length} usuarios correctamente.`,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Error al importar usuarios";
      setImportError(errorMessage);
      toast({
        title: "Error de importación",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Vista previa de importación</h2>
        <button
          onClick={onCancel}
          className="text-gray-500 hover:text-gray-700"
          disabled={isImporting}
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      {importError && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <p className="text-sm text-red-700">{importError}</p>
          </div>
        </div>
      )}

      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
            {validUsers.length} válidos
          </div>
          {invalidUsers.length > 0 && (
            <div className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
              {invalidUsers.length} inválidos
            </div>
          )}
          {duplicateUsers.length > 0 && (
            <div className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
              {duplicateUsers.length} duplicados
            </div>
          )}
        </div>

        <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-blue-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                Se importarán solo los usuarios válidos. Los duplicados e
                inválidos serán ignorados.
                <br />
                Para cada usuario importado se generará una contraseña aleatoria
                segura.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Estado
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Email
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Nombre
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Apellido
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Documento
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Detalles
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((user, index) => (
              <tr
                key={index}
                className={
                  user.status === "invalid"
                    ? "bg-red-50"
                    : user.status === "duplicate"
                      ? "bg-yellow-50"
                      : ""
                }
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  {user.status === "valid" && (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  )}
                  {user.status === "invalid" && (
                    <AlertCircle className="h-5 w-5 text-red-500" />
                  )}
                  {user.status === "duplicate" && (
                    <AlertCircle className="h-5 w-5 text-yellow-500" />
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {user.email}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {user.firstName}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {user.lastName}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {user.identityDocument}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {user.errors && user.errors.length > 0 && (
                    <div className="text-xs text-red-600">
                      {user.errors.map((error, i) => (
                        <div key={i}>{error}</div>
                      ))}
                    </div>
                  )}
                  {user.status === "duplicate" && (
                    <div className="text-xs text-yellow-600">
                      Usuario ya existe en el sistema
                    </div>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="mt-6 flex justify-end gap-3">
        <Button variant="outline" onClick={onCancel} disabled={isImporting}>
          Cancelar
        </Button>
        <Button
          onClick={handleConfirm}
          disabled={isImporting || validUsers.length === 0}
        >
          {isImporting
            ? "Importando..."
            : `Importar ${validUsers.length} usuarios`}
        </Button>
      </div>
    </div>
  );
}
