#!/usr/bin/env node

/**
 * @fileoverview Code Auditing System for QR CURSE
 *
 * Sistema automatizado de auditoría de código que incluye:
 * - Análisis de calidad de código
 * - Detección de vulnerabilidades de seguridad
 * - Análisis de dependencias
 * - Verificación de estándares de codificación
 * - Generación de reportes detallados
 */

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');

// Configuración del sistema de auditoría
const AUDIT_CONFIG = {
  // Directorios a auditar
  auditPaths: [
    'src/',
    'scripts/',
    'docs/',
  ],

  // Archivos de configuración críticos
  configFiles: [
    'package.json',
    'tsconfig.json',
    'next.config.js',
    'tailwind.config.js',
    'jest.config.js',
    '.eslintrc.json',
  ],

  // Extensiones de archivo a analizar
  fileExtensions: ['.js', '.jsx', '.ts', '.tsx', '.json', '.md'],

  // Patrones de seguridad a verificar
  securityPatterns: [
    {
      pattern: /console\.log\(/g,
      severity: 'warning',
      message: 'Console.log encontrado - considerar remover en producción',
    },
    {
      pattern: /debugger/g,
      severity: 'error',
      message: 'Debugger statement encontrado',
    },
    {
      pattern: /eval\(/g,
      severity: 'critical',
      message: 'Uso de eval() detectado - riesgo de seguridad',
    },
    {
      pattern: /innerHTML\s*=/g,
      severity: 'warning',
      message: 'Uso de innerHTML - verificar sanitización',
    },
    {
      pattern: /document\.write/g,
      severity: 'error',
      message: 'Uso de document.write detectado',
    },
  ],

  // Métricas de calidad
  qualityThresholds: {
    maxFileSize: 2000, // líneas
    maxFunctionLength: 50, // líneas
    maxComplexity: 10,
    minTestCoverage: 80, // porcentaje
  },
};

/**
 * Clase principal del sistema de auditoría
 */
class CodeAuditSystem {
  constructor() {
    this.timestamp = new Date().toISOString();
    this.auditId = crypto.randomBytes(8).toString('hex');
    this.results = {
      summary: {},
      security: [],
      quality: [],
      dependencies: {},
      files: [],
      recommendations: [],
    };
  }

  /**
   * Ejecuta el proceso completo de auditoría
   */
  async run() {
    try {
      console.log('🔍 Iniciando auditoría de código...');
      console.log(`📅 Timestamp: ${this.timestamp}`);
      console.log(`🆔 Audit ID: ${this.auditId}`);

      // Ejecutar análisis
      await this.analyzeProjectStructure();
      await this.runSecurityAudit();
      await this.analyzeCodeQuality();
      await this.auditDependencies();
      await this.runLinting();
      await this.checkTestCoverage();

      // Generar reporte
      await this.generateReport();

      console.log('✅ Auditoría completada exitosamente');

      return {
        success: true,
        auditId: this.auditId,
        timestamp: this.timestamp,
        results: this.results,
      };

    } catch (error) {
      console.error('❌ Error durante la auditoría:', error.message);
      throw error;
    }
  }

  /**
   * Analizar estructura del proyecto
   */
  async analyzeProjectStructure() {
    console.log('📁 Analizando estructura del proyecto...');

    const stats = {
      totalFiles: 0,
      totalLines: 0,
      filesByExtension: {},
      largeFiles: [],
    };

    for (const auditPath of AUDIT_CONFIG.auditPaths) {
      try {
        await this.analyzeDirectory(path.join(process.cwd(), auditPath), stats);
      } catch (error) {
        console.warn(`⚠️  No se pudo analizar: ${auditPath}`);
      }
    }

    this.results.summary = {
      totalFiles: stats.totalFiles,
      totalLines: stats.totalLines,
      filesByExtension: stats.filesByExtension,
      largeFiles: stats.largeFiles,
      timestamp: this.timestamp,
    };

    console.log(`✅ Analizados ${stats.totalFiles} archivos (${stats.totalLines} líneas)`);
  }

  /**
   * Ejecutar auditoría de seguridad
   */
  async runSecurityAudit() {
    console.log('🔒 Ejecutando auditoría de seguridad...');

    const securityIssues = [];

    // Analizar patrones de seguridad en archivos
    for (const auditPath of AUDIT_CONFIG.auditPaths) {
      try {
        await this.scanSecurityPatterns(path.join(process.cwd(), auditPath), securityIssues);
      } catch (error) {
        console.warn(`⚠️  Error escaneando seguridad en: ${auditPath}`);
      }
    }

    // Ejecutar npm audit si está disponible
    try {
      const auditOutput = execSync('npm audit --json', {
        encoding: 'utf8',
        timeout: 30000
      });

      const auditData = JSON.parse(auditOutput);

      if (auditData.vulnerabilities) {
        Object.entries(auditData.vulnerabilities).forEach(([pkg, vuln]) => {
          securityIssues.push({
            type: 'dependency',
            severity: vuln.severity,
            package: pkg,
            message: `Vulnerabilidad en dependencia: ${pkg}`,
            details: vuln,
          });
        });
      }
    } catch (error) {
      console.warn('⚠️  No se pudo ejecutar npm audit');
    }

    this.results.security = securityIssues;
    console.log(`✅ Auditoría de seguridad completada: ${securityIssues.length} issues encontrados`);
  }

  /**
   * Analizar calidad de código
   */
  async analyzeCodeQuality() {
    console.log('📊 Analizando calidad de código...');

    const qualityIssues = [];

    // Verificar archivos grandes
    this.results.summary.largeFiles.forEach(file => {
      if (file.lines > AUDIT_CONFIG.qualityThresholds.maxFileSize) {
        qualityIssues.push({
          type: 'file-size',
          severity: 'warning',
          file: file.path,
          message: `Archivo muy grande: ${file.lines} líneas (máximo recomendado: ${AUDIT_CONFIG.qualityThresholds.maxFileSize})`,
          lines: file.lines,
        });
      }
    });

    // Verificar archivos de configuración
    for (const configFile of AUDIT_CONFIG.configFiles) {
      try {
        const configPath = path.join(process.cwd(), configFile);
        await fs.access(configPath);

        // Verificar sintaxis JSON para archivos JSON
        if (configFile.endsWith('.json')) {
          try {
            const content = await fs.readFile(configPath, 'utf8');
            JSON.parse(content);
          } catch (jsonError) {
            qualityIssues.push({
              type: 'config-syntax',
              severity: 'error',
              file: configFile,
              message: `Error de sintaxis JSON en ${configFile}`,
              details: jsonError.message,
            });
          }
        }
      } catch {
        qualityIssues.push({
          type: 'missing-config',
          severity: 'warning',
          file: configFile,
          message: `Archivo de configuración faltante: ${configFile}`,
        });
      }
    }

    this.results.quality = qualityIssues;
    console.log(`✅ Análisis de calidad completado: ${qualityIssues.length} issues encontrados`);
  }

  /**
   * Auditar dependencias
   */
  async auditDependencies() {
    console.log('📦 Auditando dependencias...');

    try {
      const packageJson = JSON.parse(await fs.readFile('package.json', 'utf8'));

      const dependencyStats = {
        production: Object.keys(packageJson.dependencies || {}).length,
        development: Object.keys(packageJson.devDependencies || {}).length,
        outdated: [],
        unused: [],
      };

      // Verificar dependencias desactualizadas
      try {
        const outdatedOutput = execSync('npm outdated --json', {
          encoding: 'utf8',
          timeout: 30000
        });

        if (outdatedOutput.trim()) {
          const outdatedData = JSON.parse(outdatedOutput);
          dependencyStats.outdated = Object.keys(outdatedData);
        }
      } catch {
        // npm outdated devuelve código de salida no-cero cuando hay paquetes desactualizados
      }

      this.results.dependencies = dependencyStats;
      console.log(`✅ Auditoría de dependencias completada: ${dependencyStats.production + dependencyStats.development} dependencias`);

    } catch (error) {
      console.error('❌ Error auditando dependencias:', error.message);
    }
  }

  /**
   * Ejecutar linting
   */
  async runLinting() {
    console.log('🧹 Ejecutando linting...');

    try {
      // Intentar ejecutar ESLint
      const lintOutput = execSync('npx eslint src/ --format json', {
        encoding: 'utf8',
        timeout: 60000
      });

      const lintResults = JSON.parse(lintOutput);
      const lintIssues = [];

      lintResults.forEach(result => {
        result.messages.forEach(message => {
          lintIssues.push({
            type: 'lint',
            severity: message.severity === 2 ? 'error' : 'warning',
            file: result.filePath,
            line: message.line,
            column: message.column,
            message: message.message,
            rule: message.ruleId,
          });
        });
      });

      this.results.linting = lintIssues;
      console.log(`✅ Linting completado: ${lintIssues.length} issues encontrados`);

    } catch (error) {
      console.warn('⚠️  No se pudo ejecutar ESLint:', error.message);
      this.results.linting = [];
    }
  }

  /**
   * Verificar cobertura de tests
   */
  async checkTestCoverage() {
    console.log('🧪 Verificando cobertura de tests...');

    try {
      // Intentar ejecutar tests con cobertura
      const coverageOutput = execSync('npm test -- --coverage --watchAll=false --passWithNoTests', {
        encoding: 'utf8',
        timeout: 120000
      });

      // Extraer información de cobertura (simplificado)
      const coverageMatch = coverageOutput.match(/All files\s+\|\s+([\d.]+)/);
      const coverage = coverageMatch ? parseFloat(coverageMatch[1]) : 0;

      this.results.testCoverage = {
        percentage: coverage,
        threshold: AUDIT_CONFIG.qualityThresholds.minTestCoverage,
        meets_threshold: coverage >= AUDIT_CONFIG.qualityThresholds.minTestCoverage,
      };

      console.log(`✅ Cobertura de tests: ${coverage}%`);

    } catch (error) {
      console.warn('⚠️  No se pudo verificar cobertura de tests');
      this.results.testCoverage = {
        percentage: 0,
        threshold: AUDIT_CONFIG.qualityThresholds.minTestCoverage,
        meets_threshold: false,
        error: error.message,
      };
    }
  }

  /**
   * Generar reporte de auditoría
   */
  async generateReport() {
    console.log('📋 Generando reporte de auditoría...');

    // Generar recomendaciones
    this.generateRecommendations();

    // Crear reporte en formato JSON
    const report = {
      auditId: this.auditId,
      timestamp: this.timestamp,
      version: '1.0.0',
      results: this.results,
    };

    // Guardar reporte
    const reportPath = path.join(process.cwd(), 'audit-reports', `audit-${this.auditId}.json`);
    await fs.mkdir(path.dirname(reportPath), { recursive: true });
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

    // Generar reporte en Markdown
    await this.generateMarkdownReport(reportPath.replace('.json', '.md'));

    console.log(`✅ Reporte generado: ${reportPath}`);
  }

  /**
   * Generar recomendaciones basadas en los resultados
   */
  generateRecommendations() {
    const recommendations = [];

    // Recomendaciones de seguridad
    const criticalSecurity = this.results.security.filter(issue => issue.severity === 'critical');
    if (criticalSecurity.length > 0) {
      recommendations.push({
        priority: 'high',
        category: 'security',
        message: `Resolver ${criticalSecurity.length} problemas críticos de seguridad`,
      });
    }

    // Recomendaciones de calidad
    const largeFiles = this.results.quality.filter(issue => issue.type === 'file-size');
    if (largeFiles.length > 0) {
      recommendations.push({
        priority: 'medium',
        category: 'quality',
        message: `Refactorizar ${largeFiles.length} archivos grandes`,
      });
    }

    // Recomendaciones de dependencias
    if (this.results.dependencies.outdated && this.results.dependencies.outdated.length > 0) {
      recommendations.push({
        priority: 'medium',
        category: 'dependencies',
        message: `Actualizar ${this.results.dependencies.outdated.length} dependencias desactualizadas`,
      });
    }

    // Recomendaciones de testing
    if (this.results.testCoverage && !this.results.testCoverage.meets_threshold) {
      recommendations.push({
        priority: 'high',
        category: 'testing',
        message: `Mejorar cobertura de tests del ${this.results.testCoverage.percentage}% al ${this.results.testCoverage.threshold}%`,
      });
    }

    this.results.recommendations = recommendations;
  }

  /**
   * Generar reporte en formato Markdown
   */
  async generateMarkdownReport(filePath) {
    const markdown = `# 🔍 Reporte de Auditoría de Código

**Audit ID:** ${this.auditId}
**Fecha:** ${this.timestamp}

## 📊 Resumen Ejecutivo

- **Archivos analizados:** ${this.results.summary.totalFiles}
- **Líneas de código:** ${this.results.summary.totalLines}
- **Issues de seguridad:** ${this.results.security.length}
- **Issues de calidad:** ${this.results.quality.length}
- **Cobertura de tests:** ${this.results.testCoverage?.percentage || 0}%

## 🔒 Seguridad

${this.results.security.length === 0 ? '✅ No se encontraron problemas de seguridad' :
  this.results.security.map(issue => `- **${issue.severity.toUpperCase()}:** ${issue.message}`).join('\n')}

## 📊 Calidad de Código

${this.results.quality.length === 0 ? '✅ No se encontraron problemas de calidad' :
  this.results.quality.map(issue => `- **${issue.severity.toUpperCase()}:** ${issue.message}`).join('\n')}

## 📦 Dependencias

- **Producción:** ${this.results.dependencies.production || 0}
- **Desarrollo:** ${this.results.dependencies.development || 0}
- **Desactualizadas:** ${this.results.dependencies.outdated?.length || 0}

## 💡 Recomendaciones

${this.results.recommendations.map(rec => `- **${rec.priority.toUpperCase()}:** ${rec.message}`).join('\n')}

---

*Reporte generado automáticamente por el Sistema de Auditoría de Código*
`;

    await fs.writeFile(filePath, markdown);
  }

  // Métodos auxiliares
  async analyzeDirectory(dirPath, stats) {
    const entries = await fs.readdir(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
        await this.analyzeDirectory(fullPath, stats);
      } else if (entry.isFile()) {
        const ext = path.extname(entry.name);
        if (AUDIT_CONFIG.fileExtensions.includes(ext)) {
          const content = await fs.readFile(fullPath, 'utf8');
          const lines = content.split('\n').length;

          stats.totalFiles++;
          stats.totalLines += lines;
          stats.filesByExtension[ext] = (stats.filesByExtension[ext] || 0) + 1;

          if (lines > AUDIT_CONFIG.qualityThresholds.maxFileSize) {
            stats.largeFiles.push({ path: fullPath, lines });
          }
        }
      }
    }
  }

  async scanSecurityPatterns(dirPath, issues) {
    const entries = await fs.readdir(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
        await this.scanSecurityPatterns(fullPath, issues);
      } else if (entry.isFile()) {
        const ext = path.extname(entry.name);
        if (['.js', '.jsx', '.ts', '.tsx'].includes(ext)) {
          const content = await fs.readFile(fullPath, 'utf8');

          AUDIT_CONFIG.securityPatterns.forEach(pattern => {
            const matches = content.match(pattern.pattern);
            if (matches) {
              issues.push({
                type: 'security-pattern',
                severity: pattern.severity,
                file: fullPath,
                message: pattern.message,
                matches: matches.length,
              });
            }
          });
        }
      }
    }
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  const audit = new CodeAuditSystem();
  audit.run()
    .then(result => {
      console.log('🎉 Auditoría completada:', result);
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Error fatal:', error);
      process.exit(1);
    });
}

module.exports = { CodeAuditSystem, AUDIT_CONFIG };
