# Descripción

Por favor, incluye un resumen del cambio y qué problema soluciona. Incluye también la motivación y el contexto relevantes. Enumera las dependencias necesarias para este cambio.

Fixes # (issue)

## Tipo de cambio

Por favor, elimina las opciones que no sean relevantes.

- [ ] Corrección de error (cambio no disruptivo que soluciona un problema)
- [ ] Nueva característica (cambio no disruptivo que añade funcionalidad)
- [ ] Cambio disruptivo (cambio que haría que la funcionalidad existente no funcionara como se esperaba)
- [ ] Este cambio requiere una actualización de la documentación

# Cómo se ha probado

Por favor, describe las pruebas que has realizado para verificar tus cambios. Proporciona instrucciones para que podamos reproducirlas. Enumera también los detalles relevantes de tu configuración de prueba.

- [ ] Prueba A
- [ ] Prueba B

# Lista de verificación:

- [ ] Mi código sigue las directrices de estilo de este proyecto
- [ ] He realizado una auto-revisión de mi propio código
- [ ] He comentado mi código, especialmente en áreas difíciles de entender
- [ ] He realizado los cambios correspondientes en la documentación
- [ ] Mis cambios no generan nuevas advertencias
- [ ] He añadido pruebas que demuestran que mi corrección es efectiva o que mi característica funciona
- [ ] Las pruebas unitarias nuevas y existentes pasan localmente con mis cambios
- [ ] Cualquier cambio dependiente ha sido fusionado y publicado en los módulos posteriores
