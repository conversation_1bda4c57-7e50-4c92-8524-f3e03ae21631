name: Generate Project Metadata & Embeddings

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  metadata:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Install dependencies
        run: npm install
      - name: Extract project metadata
        run: node .github/scripts/extract_metadata.js
      - name: Generate embeddings
        run: node .github/scripts/generate_embeddings.js
      - name: Commit and push metadata
        uses: EndBug/add-and-commit@v9
        with:
          add: 'project_metadata.json project_vectors.jsonl'
          message: 'chore: update project metadata and embeddings'