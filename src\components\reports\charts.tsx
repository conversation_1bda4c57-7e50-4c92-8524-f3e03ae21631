"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  <PERSON><PERSON>hart,
  Line,
  Legend,
} from "recharts";
import {
  type CertificatesByMonth,
  type UsersByRole,
  type CoursePerformance,
} from "@/lib/data/mock-reports-data";

interface ChartsProps {
  certificatesByMonth: CertificatesByMonth[];
  usersByRole: UsersByRole[];
  coursePerformance: CoursePerformance[];
  isLoading?: boolean;
}

const COLORS = {
  primary: "#3b82f6",
  secondary: "#10b981",
  warning: "#f59e0b",
  danger: "#ef4444",
  purple: "#8b5cf6",
  teal: "#14b8a6",
  orange: "#f97316",
  pink: "#ec4899",
};

const PIE_COLORS = [COLORS.primary, COLORS.secondary, COLORS.warning, COLORS.danger];

export function Charts({
  certificatesByMonth,
  usersByRole,
  coursePerformance,
  isLoading = false,
}: ChartsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-6 md:grid-cols-2">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-5 bg-muted rounded w-32"></div>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-muted rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2">
      {/* Certificates by Month Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Certificados por Mes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={certificatesByMonth}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="month" 
                fontSize={12}
                tick={{ fill: 'currentColor' }}
              />
              <YAxis 
                fontSize={12}
                tick={{ fill: 'currentColor' }}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                }}
              />
              <Legend />
              <Bar 
                dataKey="active" 
                fill={COLORS.secondary} 
                name="Activos"
                radius={[2, 2, 0, 0]}
              />
              <Bar 
                dataKey="expired" 
                fill={COLORS.warning} 
                name="Expirados"
                radius={[2, 2, 0, 0]}
              />
              <Bar 
                dataKey="revoked" 
                fill={COLORS.danger} 
                name="Revocados"
                radius={[2, 2, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Users by Role Pie Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Usuarios por Rol
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={usersByRole}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ role, percentage }) => `${role}: ${percentage.toFixed(1)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="count"
              >
                {usersByRole.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={PIE_COLORS[index % PIE_COLORS.length]} 
                  />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Course Performance Chart */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Rendimiento de Cursos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={coursePerformance} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                type="number" 
                fontSize={12}
                tick={{ fill: 'currentColor' }}
              />
              <YAxis 
                dataKey="courseName" 
                type="category" 
                width={150}
                fontSize={11}
                tick={{ fill: 'currentColor' }}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                }}
                formatter={(value, name) => {
                  if (name === 'completionRate') {
                    return [`${value}%`, 'Tasa de Finalización'];
                  }
                  if (name === 'averageGrade') {
                    return [value, 'Calificación Promedio'];
                  }
                  if (name === 'averageAttendance') {
                    return [`${value}%`, 'Asistencia Promedio'];
                  }
                  return [value, name];
                }}
              />
              <Legend />
              <Bar 
                dataKey="completionRate" 
                fill={COLORS.primary} 
                name="Tasa de Finalización (%)"
                radius={[0, 2, 2, 0]}
              />
              <Bar 
                dataKey="averageGrade" 
                fill={COLORS.secondary} 
                name="Calificación Promedio"
                radius={[0, 2, 2, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Certificates Trend Line Chart */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Tendencia de Certificados
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={certificatesByMonth}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="month" 
                fontSize={12}
                tick={{ fill: 'currentColor' }}
              />
              <YAxis 
                fontSize={12}
                tick={{ fill: 'currentColor' }}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                }}
              />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="certificates" 
                stroke={COLORS.primary} 
                strokeWidth={3}
                name="Total Certificados"
                dot={{ fill: COLORS.primary, strokeWidth: 2, r: 4 }}
              />
              <Line 
                type="monotone" 
                dataKey="active" 
                stroke={COLORS.secondary} 
                strokeWidth={2}
                name="Activos"
                dot={{ fill: COLORS.secondary, strokeWidth: 2, r: 3 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
}
