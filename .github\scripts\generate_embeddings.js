const fs = require('fs');
const path = require('path');
// Placeholder for embedding API call
async function getEmbedding(text) {
  // TODO: Replace with real API call (OpenAI, HuggingFace, etc.)
  // For now, return a dummy vector
  return Array(384).fill(0.001 * text.length);
}

async function main() {
  const ROOT_DIR = path.resolve(__dirname, '../../');
  const METADATA_FILE = path.resolve(ROOT_DIR, 'project_metadata.json');
  const OUTPUT_FILE = path.resolve(ROOT_DIR, 'project_vectors.jsonl');
  if (!fs.existsSync(METADATA_FILE)) {
    console.error('project_metadata.json not found. Run extract_metadata.js first.');
    process.exit(1);
  }
  const metadata = JSON.parse(fs.readFileSync(METADATA_FILE, 'utf8'));
  const out = fs.createWriteStream(OUTPUT_FILE, { flags: 'w' });
  for (const file of metadata.files) {
    const text = [file.path, file.doc, file.comments, file.firstLines].filter(Boolean).join('\n').slice(0, 2000);
    const embedding = await getEmbedding(text);
    out.write(JSON.stringify({
      path: file.path,
      ext: file.ext,
      embedding,
      text: text.slice(0, 500),
    }) + '\n');
  }
  out.end();
  console.log(`Project embeddings written to ${OUTPUT_FILE}`);
}

main();