name: Biome Lint & Format

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  biome-lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Run Biome check
        run: yarn biome check .

      - name: Run Biome format (dry-run)
        run: yarn biome format .

      # Opcional: Falla el workflow si hay errores de lint/format
      - name: Fail if Biome finds issues
        run: |
          yarn biome check .