"use client";

import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { UserCombobox } from "@/components/ui/user-combobox";

interface Attendance {
  id: string;
  user_id: string;
  lesson_id: string;
  date: string;
  status: "PRESENT" | "ABSENT" | "LATE";
  comments: string;
  created_at: string;
}

interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
}

interface Lesson {
  id: string;
  title: string;
  module_id: string;
}

export default function AttendanceManagement() {
  const [attendance, setAttendance] = useState<Attendance[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [selectedLesson, setSelectedLesson] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [selectedStatus, setSelectedStatus] = useState<
    "PRESENT" | "ABSENT" | "LATE"
  >("PRESENT");
  const [comments, setComments] = useState<string>("");
  const supabase = createClientComponentClient();

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  async function fetchData() {
    try {
      setLoading(true);
      setError(null);

      // Fetch attendance with user and lesson details
      const { data: attendanceData, error: attendanceError } = await supabase
        .from("attendance")
        .select(`
          *,
          users:user_id(id, first_name, last_name, email),
          lessons:lesson_id(id, title, module_id)
        `)
        .order("date", { ascending: false });

      if (attendanceError) throw attendanceError;

      // Fetch users
      const { data: usersData, error: usersError } = await supabase
        .from("users")
        .select("id, first_name, last_name, email")
        .eq("role", "student");

      if (usersError) throw usersError;

      // Fetch lessons
      const { data: lessonsData, error: lessonsError } = await supabase
        .from("lessons")
        .select("id, title, module_id");

      if (lessonsError) throw lessonsError;

      setAttendance(attendanceData || []);
      setUsers(usersData || []);
      setLessons(lessonsData || []);
    } catch (error) {
      setError("Error al cargar los datos");
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (!selectedUser || !selectedLesson || !selectedDate) {
      setError("Por favor complete todos los campos requeridos");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const { error } = await supabase.from("attendance").insert({
        user_id: selectedUser,
        lesson_id: selectedLesson,
        date: selectedDate,
        status: selectedStatus,
        comments,
      });

      if (error) throw error;

      // Reset form and refresh data
      setSelectedUser("");
      setSelectedLesson("");
      setSelectedDate("");
      setSelectedStatus("PRESENT");
      setComments("");
      await fetchData();
    } catch (error) {
      setError("Error al guardar la asistencia");
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Gestión de Asistencia</h1>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="student">Estudiante</Label>
            <UserCombobox
              options={users.map((user) => ({
                value: user.id,
                label: `${user.first_name} ${user.last_name}`.trim(),
                email: user.email,
                identity_document: user.identity_document,
              }))}
              value={selectedUser}
              onChange={setSelectedUser}
              placeholder="Buscar estudiante por nombre, email o RUT..."
              emptyMessage="No se encontraron estudiantes."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="lesson">Lección</Label>
            <select
              id="lesson"
              value={selectedLesson}
              onChange={(e) => setSelectedLesson(e.target.value)}
              className="w-full p-2 border rounded"
            >
              <option value="">Seleccionar lección</option>
              {lessons.map((lesson) => (
                <option key={lesson.id} value={lesson.id}>
                  {lesson.title}
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="date">Fecha</Label>
            <Input
              id="date"
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Estado</Label>
            <select
              id="status"
              value={selectedStatus}
              onChange={(e) =>
                setSelectedStatus(
                  e.target.value as "PRESENT" | "ABSENT" | "LATE",
                )
              }
              className="w-full p-2 border rounded"
            >
              <option value="PRESENT">Presente</option>
              <option value="ABSENT">Ausente</option>
              <option value="LATE">Atrasado</option>
            </select>
          </div>

          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="comments">Comentarios</Label>
            <Input
              id="comments"
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              placeholder="Comentarios opcionales"
            />
          </div>
        </div>

        <Button type="submit" disabled={loading}>
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Guardando...
            </>
          ) : (
            "Registrar Asistencia"
          )}
        </Button>
      </form>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Estudiante</TableHead>
              <TableHead>Lección</TableHead>
              <TableHead>Fecha</TableHead>
              <TableHead>Estado</TableHead>
              <TableHead>Comentarios</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {attendance.map((record) => (
              <TableRow key={record.id}>
                <TableCell>
                  {(record.users as any)?.first_name &&
                  (record.users as any)?.last_name
                    ? `${(record.users as any).first_name} ${(record.users as any).last_name}`.trim()
                    : "No disponible"}
                </TableCell>
                <TableCell>
                  {(record.lessons as any)?.title || "No disponible"}
                </TableCell>
                <TableCell>
                  {new Date(record.date).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  {record.status === "PRESENT" && "Presente"}
                  {record.status === "ABSENT" && "Ausente"}
                  {record.status === "LATE" && "Atrasado"}
                </TableCell>
                <TableCell>{record.comments || "-"}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
