# 📚 Documentation Index

Welcome to the documentation hub for this scaffold. This index provides a clear, modular map of all project manuals and guides, following the [Context Engineering Template](https://github.com/iberi22/context-engineering-template) style.

For a high-level overview, see the [README](../README.md).

---

## Roadmap & Modernization Plan

See the [Modernization Plan in README](../README.md#-plan-de-modernización-y-mejora-continua-faseshitos) for a detailed, phased approach to project improvement, including:

- Branding & Documentation Unification
- Architecture & Adapter Refactor
- CI/CD & Automation
- AI Context & Bot Orchestration
- Onboarding & Sustainability

Each phase is cross-referenced in the relevant manual below.

---

## Table of Contents

| Document                                 | Purpose/Contents                                                      | Phase |
|------------------------------------------|----------------------------------------------------------------------|-------|
| [PLANNING.md](./PLANNING.md)             | Project constitution: vision, principles, context, epics, structure   | Core  |
| [RULES.md](./RULES.md)                   | Quick rulebook: stack, workflow, coding principles, usage guide       | Core  |
| [TASKS.md](./TASKS.md)                   | Task board: granular tasks, milestones, progress tracking             | Core  |
| [AI_CONTEXT.md](./AI_CONTEXT.md)         | AI/LLM context: bot orchestration, metadata, embeddings, automation   | P4    |
| [SECURITY.md](./SECURITY.md)             | Security manual: RLS, secrets, audit logging, best practices          | All   |
| [BACKUP_MIGRATION.md](./BACKUP_MIGRATION.md) | Backup & migration: guides, automation, best practices            | P3    |
| [DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md)   | Design system: branding, components, accessibility, customization     | P1    |
| [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md) | Advanced architecture & sustainability best practices | P2    |

**Phase Legend:**

- **Core**: Essential documents for project understanding
- **P1**: Phase 1 - Documentation Unification & Modularization
- **P2**: Phase 2 - Code Architecture Refactoring
- **P3**: Phase 3 - Automation Implementation
- **P4**: Phase 4 - AI Context & Bot Orchestration
- **All**: Cross-cutting concerns across all phases

---

## Quick Navigation

### 🚀 Getting Started

1. **New to the project?** Start with [README.md](../README.md) for overview
2. **Understanding the vision?** Read [PLANNING.md](./PLANNING.md) for project constitution
3. **Need quick rules?** Check [RULES.md](./RULES.md) for development guidelines
4. **Track progress?** See [TASKS.md](./TASKS.md) for current status

### 🔧 Development & Architecture

- **Code Architecture:** [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md)
- **Security Guidelines:** [SECURITY.md](./SECURITY.md)
- **Design System:** [DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md)
- **Backup & Migration:** [BACKUP_MIGRATION.md](./BACKUP_MIGRATION.md)

### 🤖 AI & Automation

- **AI Context & Bots:** [AI_CONTEXT.md](./AI_CONTEXT.md)
- **Modernization Phases:** [README.md](../README.md#-plan-de-modernización-y-mejora-continua-faseshitos)

### 📁 Project Structure

- **Source Code:** `../src/` (Next.js app, components, utilities)
- **Documentation:** `../docs/` (this directory)
- **Configuration:** `../` (package.json, next.config.js, etc.)

---

## How to Use

- Start with **PLANNING.md** for vision and structure.
- Use **RULES.md** for quick reference on stack, workflow, and coding standards.
- Track progress and tasks in **TASKS.md**.
- For AI/bot integration, see **AI_CONTEXT.md**.
- For security, backup, design, and architecture, see the respective manuals above.
- For the full modernization plan, see the [README](../README.md#-plan-de-modernización-y-mejora-continua-faseshitos).

> Keep this index up-to-date as you add or reorganize documentation.