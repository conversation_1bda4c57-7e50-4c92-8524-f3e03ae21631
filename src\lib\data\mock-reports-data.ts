// Mock data for reports development
// This file contains simulated data for testing the reports module

export interface ReportMetrics {
  totalCertificates: number;
  activeCertificates: number;
  expiredCertificates: number;
  revokedCertificates: number;
  totalUsers: number;
  activeUsers: number;
  totalCourses: number;
  completedCourses: number;
  averageAttendance: number;
  averageGrade: number;
}

export interface CertificatesByMonth {
  month: string;
  certificates: number;
  active: number;
  expired: number;
  revoked: number;
}

export interface UsersByRole {
  role: string;
  count: number;
  percentage: number;
}

export interface CoursePerformance {
  courseId: string;
  courseName: string;
  totalEnrollments: number;
  completions: number;
  completionRate: number;
  averageGrade: number;
  averageAttendance: number;
}

export interface InstructorStats {
  instructorId: string;
  instructorName: string;
  totalCourses: number;
  totalStudents: number;
  averageRating: number;
  certificatesIssued: number;
}

// Mock data
export const mockReportMetrics: ReportMetrics = {
  totalCertificates: 1247,
  activeCertificates: 1089,
  expiredCertificates: 134,
  revokedCertificates: 24,
  totalUsers: 2456,
  activeUsers: 2234,
  totalCourses: 45,
  completedCourses: 38,
  averageAttendance: 87.5,
  averageGrade: 8.2,
};

export const mockCertificatesByMonth: CertificatesByMonth[] = [
  { month: "Ene 2024", certificates: 89, active: 78, expired: 8, revoked: 3 },
  { month: "Feb 2024", certificates: 102, active: 95, expired: 5, revoked: 2 },
  { month: "Mar 2024", certificates: 134, active: 125, expired: 7, revoked: 2 },
  { month: "Abr 2024", certificates: 156, active: 142, expired: 12, revoked: 2 },
  { month: "May 2024", certificates: 178, active: 165, expired: 10, revoked: 3 },
  { month: "Jun 2024", certificates: 145, active: 134, expired: 9, revoked: 2 },
  { month: "Jul 2024", certificates: 167, active: 152, expired: 13, revoked: 2 },
  { month: "Ago 2024", certificates: 189, active: 175, expired: 11, revoked: 3 },
  { month: "Sep 2024", certificates: 123, active: 112, expired: 8, revoked: 3 },
  { month: "Oct 2024", certificates: 98, active: 89, expired: 7, revoked: 2 },
  { month: "Nov 2024", certificates: 87, active: 78, expired: 7, revoked: 2 },
  { month: "Dic 2024", certificates: 79, active: 72, expired: 5, revoked: 2 },
];

export const mockUsersByRole: UsersByRole[] = [
  { role: "Estudiantes", count: 2156, percentage: 87.8 },
  { role: "Instructores", count: 234, percentage: 9.5 },
  { role: "Administradores", count: 45, percentage: 1.8 },
  { role: "Representantes", count: 21, percentage: 0.9 },
];

export const mockCoursePerformance: CoursePerformance[] = [
  {
    courseId: "1",
    courseName: "Primeros Auxilios Básicos",
    totalEnrollments: 245,
    completions: 234,
    completionRate: 95.5,
    averageGrade: 8.7,
    averageAttendance: 92.3,
  },
  {
    courseId: "2",
    courseName: "Seguridad Industrial",
    totalEnrollments: 189,
    completions: 167,
    completionRate: 88.4,
    averageGrade: 8.2,
    averageAttendance: 89.1,
  },
  {
    courseId: "3",
    courseName: "Manejo de Materiales Peligrosos",
    totalEnrollments: 156,
    completions: 142,
    completionRate: 91.0,
    averageGrade: 8.5,
    averageAttendance: 90.8,
  },
  {
    courseId: "4",
    courseName: "Prevención de Riesgos",
    totalEnrollments: 298,
    completions: 276,
    completionRate: 92.6,
    averageGrade: 8.4,
    averageAttendance: 88.7,
  },
  {
    courseId: "5",
    courseName: "Ergonomía Laboral",
    totalEnrollments: 134,
    completions: 118,
    completionRate: 88.1,
    averageGrade: 7.9,
    averageAttendance: 85.4,
  },
];

export const mockInstructorStats: InstructorStats[] = [
  {
    instructorId: "1",
    instructorName: "Dr. María González",
    totalCourses: 8,
    totalStudents: 456,
    averageRating: 4.8,
    certificatesIssued: 423,
  },
  {
    instructorId: "2",
    instructorName: "Ing. Carlos Rodríguez",
    totalCourses: 6,
    totalStudents: 298,
    averageRating: 4.6,
    certificatesIssued: 276,
  },
  {
    instructorId: "3",
    instructorName: "Dra. Ana Martínez",
    totalCourses: 5,
    totalStudents: 234,
    averageRating: 4.9,
    certificatesIssued: 218,
  },
  {
    instructorId: "4",
    instructorName: "Prof. Luis Hernández",
    totalCourses: 7,
    totalStudents: 389,
    averageRating: 4.5,
    certificatesIssued: 356,
  },
];

// Utility functions for generating additional mock data
export const generateMockDataForDateRange = (
  startDate: Date,
  endDate: Date
): CertificatesByMonth[] => {
  const months = [];
  const current = new Date(startDate);
  
  while (current <= endDate) {
    const monthName = current.toLocaleDateString('es-ES', { 
      month: 'short', 
      year: 'numeric' 
    });
    
    months.push({
      month: monthName,
      certificates: Math.floor(Math.random() * 200) + 50,
      active: Math.floor(Math.random() * 180) + 40,
      expired: Math.floor(Math.random() * 15) + 5,
      revoked: Math.floor(Math.random() * 5) + 1,
    });
    
    current.setMonth(current.getMonth() + 1);
  }
  
  return months;
};

export const getFilteredMockData = (filters: {
  startDate?: string;
  endDate?: string;
  courseId?: string;
  instructorId?: string;
  status?: string;
}) => {
  // In a real implementation, this would filter the actual data
  // For now, we return the mock data as-is
  return {
    metrics: mockReportMetrics,
    certificatesByMonth: mockCertificatesByMonth,
    usersByRole: mockUsersByRole,
    coursePerformance: mockCoursePerformance,
    instructorStats: mockInstructorStats,
  };
};
