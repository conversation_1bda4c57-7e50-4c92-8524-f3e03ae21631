# UI Component Guide

This guide documents all UI components in the QR CURSE platform, their usage patterns, and design principles.

## Table of Contents

- [Design System Overview](#design-system-overview)
- [Theme System](#theme-system)
- [Base Components](#base-components)
- [Form Components](#form-components)
- [Navigation Components](#navigation-components)
- [Layout Components](#layout-components)
- [Data Display Components](#data-display-components)
- [Component Patterns](#component-patterns)

## Design System Overview

The QR CURSE platform uses a modern design system built on:

- **Shadcn/UI**: Base component library
- **Radix UI**: Accessible primitives
- **Tailwind CSS**: Utility-first styling
- **Lucide React**: Icon system
- **Next Themes**: Theme management

### Design Principles

1. **Accessibility First**: All components follow WCAG 2.1 AA guidelines
2. **Responsive Design**: Mobile-first approach with breakpoint consistency
3. **Theme Consistency**: Dark/light mode support throughout
4. **Performance**: Optimized for fast loading and smooth interactions
5. **Developer Experience**: Clear APIs and comprehensive documentation

## Theme System

### ThemeProvider

The root theme provider that enables dark/light mode switching with system preference detection.

```tsx
import { ThemeProvider } from '@/components/theme/theme-provider';

function App({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      {children}
    </ThemeProvider>
  );
}
```

### ThemeToggle Components

#### Full ThemeToggle

Complete theme switcher with dropdown menu for light/dark/system options.

```tsx
import { ThemeToggle } from '@/components/theme/theme-toggle';

// Basic usage
<ThemeToggle />

// With custom styling
<ThemeToggle
  variant="outline"
  size="sm"
  className="custom-class"
  showLabels={false}
/>

// Simple toggle (light/dark only)
<ThemeToggle simple />
```

#### CompactThemeToggle

Minimal theme toggle for space-constrained layouts.

```tsx
import { CompactThemeToggle } from '@/components/theme/theme-toggle';

<CompactThemeToggle className="h-8 w-8" />
```

#### ThemeStatus

Display current theme status.

```tsx
import { ThemeStatus } from '@/components/theme/theme-toggle';

<ThemeStatus className="text-sm text-muted-foreground" />
```

### Theme-Aware Styling

Use the `useThemeAware` hook for theme-dependent logic:

```tsx
import { useThemeAware } from '@/components/theme/theme-toggle';

function MyComponent() {
  const { isDark, isLight, effectiveTheme } = useThemeAware();

  return (
    <div className={`
      ${isDark ? 'border-gray-700' : 'border-gray-200'}
      ${isLight ? 'bg-white' : 'bg-gray-900'}
    `}>
      Current theme: {effectiveTheme}
    </div>
  );
}
```

## Base Components

### Button

Versatile button component with multiple variants and states.

```tsx
import { Button } from '@/components/ui/button';

// Variants
<Button variant="default">Default</Button>
<Button variant="destructive">Delete</Button>
<Button variant="outline">Outline</Button>
<Button variant="secondary">Secondary</Button>
<Button variant="ghost">Ghost</Button>
<Button variant="link">Link</Button>

// Sizes
<Button size="default">Default</Button>
<Button size="sm">Small</Button>
<Button size="lg">Large</Button>
<Button size="icon">🔍</Button>

// States
<Button disabled>Disabled</Button>
<Button loading>Loading...</Button>

// With icons
<Button>
  <PlusIcon className="mr-2 h-4 w-4" />
  Add Item
</Button>
```

### Input

Form input component with validation states and accessibility features.

```tsx
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

// Basic input
<div className="space-y-2">
  <Label htmlFor="email">Email</Label>
  <Input
    id="email"
    type="email"
    placeholder="Enter your email"
    value={email}
    onChange={(e) => setEmail(e.target.value)}
  />
</div>

// With validation state
<Input
  type="text"
  placeholder="Username"
  className={errors.username ? "border-red-500" : ""}
  aria-invalid={!!errors.username}
  aria-describedby={errors.username ? "username-error" : undefined}
/>
{errors.username && (
  <p id="username-error" className="text-sm text-red-500">
    {errors.username}
  </p>
)}
```

### Card

Container component for grouping related content.

```tsx
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

<Card>
  <CardHeader>
    <CardTitle>Certificate Details</CardTitle>
    <CardDescription>
      View and manage certificate information
    </CardDescription>
  </CardHeader>
  <CardContent>
    <p>Certificate content goes here...</p>
  </CardContent>
  <CardFooter>
    <Button>Download</Button>
  </CardFooter>
</Card>
```

## Form Components

### Form with Validation

Complete form setup with validation using React Hook Form and Zod.

```tsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';

const formSchema = z.object({
  email: z.string().email('Invalid email address'),
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
});

function UserForm() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      firstName: '',
      lastName: '',
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values);
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="Enter your email" {...field} />
              </FormControl>
              <FormDescription>
                We'll never share your email with anyone else.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="firstName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>First Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter your first name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit">Submit</Button>
      </form>
    </Form>
  );
}
```

### Select Component

Dropdown selection component with search and multi-select capabilities.

```tsx
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Basic select
<Select value={selectedRole} onValueChange={setSelectedRole}>
  <SelectTrigger>
    <SelectValue placeholder="Select a role" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="admin">Administrator</SelectItem>
    <SelectItem value="instructor">Instructor</SelectItem>
    <SelectItem value="student">Student</SelectItem>
  </SelectContent>
</Select>

// With form integration
<FormField
  control={form.control}
  name="role"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Role</FormLabel>
      <Select onValueChange={field.onChange} defaultValue={field.value}>
        <FormControl>
          <SelectTrigger>
            <SelectValue placeholder="Select a role" />
          </SelectTrigger>
        </FormControl>
        <SelectContent>
          <SelectItem value="student">Student</SelectItem>
          <SelectItem value="instructor">Instructor</SelectItem>
        </SelectContent>
      </Select>
      <FormMessage />
    </FormItem>
  )}
/>
```

## Navigation Components

### Navigation Bar

Main navigation component with responsive design and theme integration.

```tsx
import { Navigation } from '@/components/ui/navigation';

// The navigation component automatically handles:
// - Responsive mobile menu
// - Theme toggle integration
// - User authentication state
// - Role-based menu items

<Navigation />
```

### Breadcrumbs

Navigation breadcrumbs for hierarchical navigation.

```tsx
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';

<Breadcrumb>
  <BreadcrumbList>
    <BreadcrumbItem>
      <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
    </BreadcrumbItem>
    <BreadcrumbSeparator />
    <BreadcrumbItem>
      <BreadcrumbLink href="/users">Users</BreadcrumbLink>
    </BreadcrumbItem>
    <BreadcrumbSeparator />
    <BreadcrumbItem>
      <BreadcrumbPage>Create User</BreadcrumbPage>
    </BreadcrumbItem>
  </BreadcrumbList>
</Breadcrumb>
```

## Layout Components

### Dashboard Layout

Standard layout for dashboard pages with sidebar and main content area.

```tsx
// Used in layout.tsx files
export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="flex">
        <Sidebar />
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
```

### Grid System

Responsive grid layouts using CSS Grid and Flexbox.

```tsx
// Responsive grid
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {items.map(item => (
    <Card key={item.id}>
      {/* Card content */}
    </Card>
  ))}
</div>

// Flexible layout
<div className="flex flex-col lg:flex-row gap-6">
  <div className="lg:w-2/3">
    {/* Main content */}
  </div>
  <div className="lg:w-1/3">
    {/* Sidebar content */}
  </div>
</div>
```

## Data Display Components

### Table

Data table component with sorting, filtering, and pagination.

```tsx
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

<Table>
  <TableHeader>
    <TableRow>
      <TableHead>Name</TableHead>
      <TableHead>Email</TableHead>
      <TableHead>Role</TableHead>
      <TableHead>Status</TableHead>
      <TableHead className="text-right">Actions</TableHead>
    </TableRow>
  </TableHeader>
  <TableBody>
    {users.map((user) => (
      <TableRow key={user.id}>
        <TableCell className="font-medium">
          {user.first_name} {user.last_name}
        </TableCell>
        <TableCell>{user.email}</TableCell>
        <TableCell>
          <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
            {user.role}
          </Badge>
        </TableCell>
        <TableCell>
          <Badge variant={user.is_active ? 'success' : 'destructive'}>
            {user.is_active ? 'Active' : 'Inactive'}
          </Badge>
        </TableCell>
        <TableCell className="text-right">
          <Button variant="ghost" size="sm">
            Edit
          </Button>
        </TableCell>
      </TableRow>
    ))}
  </TableBody>
</Table>
```

### Badge

Status and category indicators.

```tsx
import { Badge } from '@/components/ui/badge';

// Variants
<Badge variant="default">Default</Badge>
<Badge variant="secondary">Secondary</Badge>
<Badge variant="destructive">Error</Badge>
<Badge variant="outline">Outline</Badge>

// Custom styling
<Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
  Success
</Badge>
```

## Component Patterns

### Loading States

Consistent loading patterns across the application.

```tsx
import { Skeleton } from '@/components/ui/skeleton';
import { Spinner } from '@/components/ui/spinner';

// Skeleton loading
<div className="space-y-4">
  <Skeleton className="h-4 w-[250px]" />
  <Skeleton className="h-4 w-[200px]" />
  <Skeleton className="h-4 w-[150px]" />
</div>

// Spinner loading
<div className="flex items-center justify-center p-8">
  <Spinner size="lg" />
</div>

// Button loading state
<Button disabled={isLoading}>
  {isLoading && <Spinner size="sm" className="mr-2" />}
  {isLoading ? 'Saving...' : 'Save'}
</Button>
```

### Error States

Error handling and display patterns.

```tsx
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';

// Error alert
<Alert variant="destructive">
  <AlertTriangle className="h-4 w-4" />
  <AlertTitle>Error</AlertTitle>
  <AlertDescription>
    Something went wrong. Please try again.
  </AlertDescription>
</Alert>

// Form field error
<div className="space-y-2">
  <Input
    className={errors.email ? "border-red-500" : ""}
    aria-invalid={!!errors.email}
  />
  {errors.email && (
    <p className="text-sm text-red-500">{errors.email}</p>
  )}
</div>
```

### Empty States

Patterns for displaying empty or no-data states.

```tsx
import { FileX } from 'lucide-react';

function EmptyState({
  icon: Icon = FileX,
  title = "No data found",
  description = "There are no items to display.",
  action
}: EmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <Icon className="h-12 w-12 text-muted-foreground mb-4" />
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground mb-4 max-w-sm">{description}</p>
      {action}
    </div>
  );
}

// Usage
<EmptyState
  title="No certificates found"
  description="You haven't issued any certificates yet."
  action={
    <Button>
      <PlusIcon className="mr-2 h-4 w-4" />
      Issue Certificate
    </Button>
  }
/>
```

This guide provides a comprehensive overview of the UI component system in QR CURSE. Each component is designed to be accessible, themeable, and consistent with the overall design system.
