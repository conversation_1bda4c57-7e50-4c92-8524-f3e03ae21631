# Database Views

This document outlines the views implemented in the database.

## user_info

A view that provides the current user's information by joining the public.users and auth.users tables.

```sql
CREATE OR REPLACE VIEW public.user_info AS
SELECT 
  u.id,
  u.email,
  u.first_name,
  u.last_name,
  u.role,
  u.company_id,
  u.created_at,
  u.updated_at,
  u.is_active,
  u.phone,
  u.identity_document,
  au.last_sign_in_at
FROM 
  users u
JOIN 
  auth.users au ON u.id = au.id
WHERE 
  auth.uid() = u.id;
```

### Usage

This view is used to easily access the current user's information, including data from both the public.users and auth.users tables.

```javascript
// Example usage in JavaScript
const { data, error } = await supabase
  .from('user_info')
  .select('*');
```

### Security Considerations

- The view is automatically filtered to only show the current user's information
- It joins the public.users and auth.users tables to provide complete user information
- It includes the last_sign_in_at field from auth.users, which is not directly available in public.users
