"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import { tableExists } from "@/lib/supabase"; // Import tableExists instead

export default function AdminPanelError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const [dbStatus, setDbStatus] = useState<{
    checked: boolean;
    tables: string[];
    error?: string;
  }>({
    checked: false,
    tables: [],
  });
  const [diagnosticInfo, setDiagnosticInfo] = useState("");
  const [isRunningDiagnostic, setIsRunningDiagnostic] = useState(false);

  useEffect(() => {
    // Registrar el error en un servicio de monitoreo
    console.error("Error en panel de administración:", error);

    // Intentar diagnosticar la conexión de la base de datos
    // Modified checkDatabase to use tableExists for key tables
    async function checkDatabase() {
      try {
        const requiredTables = ["users", "certificates", "courses"];
        const results = await Promise.all(
          requiredTables.map(async (tableName) => ({
            name: tableName,
            exists: await tableExists(tableName),
          })),
        );
        const existingTables = results
          .filter((r) => r.exists)
          .map((r) => r.name);
        const missingTables = results
          .filter((r) => !r.exists)
          .map((r) => r.name);

        setDbStatus({
          checked: true,
          tables: existingTables,
          error:
            missingTables.length > 0
              ? `Faltan tablas clave: ${missingTables.join(", ")}`
              : undefined,
        });
      } catch (err) {
        console.error("Error checking database in error boundary:", err);
        setDbStatus({
          checked: true,
          tables: [],
          error: `Error inesperado al verificar tablas: ${err instanceof Error ? err.message : "Error desconocido"}`,
        });
      }
    }

    checkDatabase();
  }, [error]);

  const handleDiagnose = async () => {
    setIsRunningDiagnostic(true);
    try {
      // Modified handleDiagnose to use tableExists
      const requiredTables = [
        "users",
        "certificates",
        "courses",
        "enrollments",
        "attendance",
        "grades",
      ]; // Check more tables on manual diagnose
      const results = await Promise.all(
        requiredTables.map(async (tableName) => ({
          name: tableName,
          exists: await tableExists(tableName),
        })),
      );
      const existingTables = results.filter((r) => r.exists).map((r) => r.name);
      const missingTables = results.filter((r) => !r.exists).map((r) => r.name);

      let diagMessage = `Tablas verificadas: ${existingTables.join(", ") || "Ninguna"}.`;
      if (missingTables.length > 0) {
        diagMessage += ` Tablas faltantes o inaccesibles: ${missingTables.join(", ")}.`;
      }

      setDiagnosticInfo(diagMessage);
    } catch (diagError) {
      setDiagnosticInfo(
        `Error durante el diagnóstico: ${diagError instanceof Error ? diagError.message : "Error desconocido"}`,
      );
    } finally {
      setIsRunningDiagnostic(false);
    }
  };

  // Determinar si es un error de recursos
  const isResourceError =
    error.message?.includes("load resource") ||
    error.message?.includes("status of 400") ||
    error.message?.includes("fetchDashboard");

  const isDbError =
    isResourceError ||
    error.message?.includes("database") ||
    error.message?.includes("supabase") ||
    error.message?.includes("table");

  const errorTitle = isDbError
    ? "Error de Conexión con la Base de Datos"
    : "Error en el Panel de Administración";

  const errorMessage = isDbError
    ? "No se pudieron cargar los recursos necesarios desde la base de datos. Esto puede deberse a problemas de conexión o a que las tablas requeridas no existen."
    : error.message ||
      "Ha ocurrido un error inesperado en el panel de administración.";

  return (
    <div className="min-h-[70vh] flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{errorTitle}</h2>
          <div className="bg-red-50 p-4 rounded-md mb-6">
            <p className="text-sm text-red-800">{errorMessage}</p>

            {isDbError && dbStatus.checked && (
              <div className="mt-4 text-left">
                <p className="text-xs font-medium text-gray-700">
                  Diagnóstico de base de datos:
                </p>
                {dbStatus.error ? (
                  <p className="text-xs text-red-600 mt-1">{dbStatus.error}</p>
                ) : (
                  <>
                    <p className="text-xs text-gray-600 mt-1">
                      Tablas clave verificadas:{" "}
                      {
                        dbStatus.tables.length > 0
                          ? dbStatus.tables.length
                          : "Ninguna" // Should show users, certificates, courses if OK
                      }
                    </p>
                    {dbStatus.tables.length > 0 && (
                      <ul className="mt-1 text-xs text-gray-500 space-y-1">
                        {dbStatus.tables.slice(0, 8).map((table) => (
                          <li key={table}>✓ {table}</li>
                        ))}
                        {dbStatus.tables.length > 8 && (
                          <li>... y {dbStatus.tables.length - 8} más</li>
                        )}
                      </ul>
                    )}
                  </>
                )}
              </div>
            )}

            {error.digest && (
              <p className="mt-2 text-xs text-gray-500">
                Código de error: {error.digest}
              </p>
            )}

            {/* Información de diagnóstico */}
            {diagnosticInfo && (
              <div className="mt-4 p-4 bg-gray-100 rounded-md text-sm">
                <p className="font-medium">Información de diagnóstico:</p>
                <p className="mt-1 text-gray-700">{diagnosticInfo}</p>
              </div>
            )}
          </div>
          <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3 justify-center">
            <button
              onClick={() => reset()}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Intentar de nuevo
            </button>
            <button
              onClick={handleDiagnose}
              disabled={isRunningDiagnostic}
              className="px-4 py-2 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {isRunningDiagnostic
                ? "Diagnosticando..."
                : "Diagnosticar conexión"}
            </button>
            <Link
              href="/panel-admin"
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              Volver al Dashboard
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
