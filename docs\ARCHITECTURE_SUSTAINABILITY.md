# 🏗️ Architecture & Sustainability Best Practices

> **See Also:** [INDEX.md](./INDEX.md) | [PLANNING.md](./PLANNING.md) | [RULES.md](./RULES.md)

This document outlines robust, industry-standard practices to ensure the long-term maintainability, adaptability, and quality of the QR Course Scaffold.

---

## Modernization Roadmap Reference

- See the [README](../README.md#-plan-de-modernización-y-mejora-continua-faseshitos) for the full phased plan.
- This document is Phase 2 of the modernization roadmap.
- All architecture and sustainability practices are cross-referenced in [PLANNING.md](./PLANNING.md), [INDEX.md](./INDEX.md), and [RULES.md](./RULES.md).

---

## 1. Minimizing External Dependencies

- **Audit all dependencies regularly:**
  - Use only what is essential for your use case.
  - Example: If you use Radix UI for just a few components, consider replacing them with custom or Shadcn/ui components.
- **Business logic (validation, rules, utilities):**
  - Prefer native JS/TS or your own utilities over small third-party packages.
  - Example: Use built-in `Array`, `Date`, or `Intl` APIs for formatting and validation.
- **Authentication and access:**
  - Supabase Auth is robust, but document your auth logic and use adapters so you can migrate to another provider if needed.
  - Example: Abstract auth flows in `src/lib/auth-helpers.ts` and avoid direct calls to Supabase in your business logic.

---

## 2. Modularity & Adaptability

- **Separate business logic from infrastructure:**
  - Use "ports and adapters" (hexagonal/clean architecture) so your core logic is not tied to Next.js, Supabase, or any specific provider.
  - Example: Place domain logic in `src/lib/` and use adapters for Supabase, email, storage, etc.
- **Document every module and its context:**
  - Add clear comments and context to each file/module/component.
  - Example: Each file should start with a docstring explaining its purpose and usage.
- **Explicit boundaries:**
  - Make it clear where framework-specific code ends and your business logic begins.

---

## 3. Automation & Observability

- **Automate metadata and embedding generation:**
  - Use scripts and workflows to keep LLM/AI context always up-to-date.
- **Health-check and dependency audit scripts:**
  - Regularly check for unused or vulnerable dependencies.
  - Example: Use `npm ls`, `npm audit`, and custom scripts to report issues.
- **Logging and error tracking:**
  - Integrate a solution like Sentry, LogRocket, or your own logging for all critical flows.
  - Example: Add error boundaries in React and log errors to a central service.

---

## 4. Testing & Security

- **Increase test coverage for custom logic:**
  - Focus on business rules, adapters, and critical flows.
  - Example: Use Vitest for unit tests and Playwright for E2E.
- **Audit and document RLS policies and secrets management:**
  - Keep all RLS policies versioned and documented in `docs/` or as SQL migrations.
  - Store secrets in `.env.local` and never commit them to git.
- **Backup and migration testing:**
  - Include scripts to test backup restoration and migration flows.
  - Example: Regularly restore backups to a test environment and verify data integrity.

---

## 5. Actionable Recommendations

- **Review dependencies quarterly and after major upgrades.**
- **Abstract all third-party integrations behind adapters.**
- **Document every module/component with a clear docstring.**
- **Automate all repetitive tasks (metadata, audit, backup, etc.).**
- **Integrate logging and error tracking from the start.**
- **Maintain high test coverage and run tests on every PR.**
- **Version and document all security policies and migrations.**

---

> Following these practices ensures your scaffold remains modern, maintainable, and ready for both human and AI contributors for years to come.