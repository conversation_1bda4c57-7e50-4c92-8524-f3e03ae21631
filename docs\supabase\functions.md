# Database Functions

This document outlines the functions implemented in the database.

## Security Functions

These functions are used primarily for Row Level Security (RLS) policies.

### is_admin()

Checks if the current user has the admin role.

```sql
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
  -- Direct query to auth.users to get role from app_metadata
  -- This avoids recursion by not querying the public.users table
  RETURN EXISTS (
    SELECT 1
    FROM auth.users
    WHERE
      id = auth.uid() AND
      raw_app_meta_data->>'role' = 'admin'
  );
END;
$function$
```

#### Usage

This function is used in RLS policies to restrict certain operations to admin users only.

#### Security Considerations

- Uses `SECURITY DEFINER` to run with the privileges of the function creator
- Directly queries `auth.users` to avoid recursion with the `public.users` table
- Checks the `raw_app_meta_data` JSON field for the role information

### is_instructor()

Checks if the current user has the instructor role.

```sql
CREATE OR REPLACE FUNCTION public.is_instructor()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
  -- Direct query to auth.users to get role from app_metadata
  -- This avoids recursion by not querying the public.users table
  RETURN EXISTS (
    SELECT 1
    FROM auth.users
    WHERE
      id = auth.uid() AND
      raw_app_meta_data->>'role' = 'instructor'
  );
END;
$function$
```

#### Usage

This function is used in RLS policies to grant instructors access to relevant data.

#### Security Considerations

- Uses `SECURITY DEFINER` to run with the privileges of the function creator
- Directly queries `auth.users` to avoid recursion with the `public.users` table
- Checks the `raw_app_meta_data` JSON field for the role information

### is_own_profile(user_id)

Checks if the current user is accessing their own profile.

```sql
CREATE OR REPLACE FUNCTION public.is_own_profile(user_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
  RETURN auth.uid() = user_id;
END;
$function$
```

#### Usage

This function is used in RLS policies to allow users to access and modify their own data.

#### Security Considerations

- Uses `SECURITY DEFINER` to run with the privileges of the function creator
- Simply compares the provided user_id with the current user's ID from auth.uid()

### get_user_info(user_id)

Retrieves user information from both public.users and auth.users tables.

```sql
CREATE OR REPLACE FUNCTION public.get_user_info(user_id uuid DEFAULT NULL::uuid)
 RETURNS TABLE(id uuid, email text, first_name text, last_name text, role user_role, company_id uuid, created_at timestamp with time zone, updated_at timestamp with time zone, is_active boolean, phone numeric, identity_document text, last_sign_in_at timestamp with time zone)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  -- If no user_id is provided, use the current user's ID
  IF user_id IS NULL THEN
    user_id := auth.uid();
  END IF;

  -- Check if the current user is an admin or is requesting their own info
  IF public.is_admin() OR public.is_own_profile(user_id) THEN
    RETURN QUERY
    SELECT
      u.id,
      u.email,
      u.first_name,
      u.last_name,
      u.role,
      u.company_id,
      u.created_at,
      u.updated_at,
      u.is_active,
      u.phone,
      u.identity_document,
      au.last_sign_in_at
    FROM
      public.users u
    JOIN
      auth.users au ON u.id = au.id
    WHERE
      u.id = user_id;
  ELSE
    -- Return empty result if not authorized
    RETURN QUERY
    SELECT
      NULL::uuid,
      NULL::text,
      NULL::text,
      NULL::text,
      NULL::user_role,
      NULL::uuid,
      NULL::timestamptz,
      NULL::timestamptz,
      NULL::boolean,
      NULL::numeric,
      NULL::text,
      NULL::timestamptz
    WHERE FALSE;
  END IF;
END;
$function$
```

#### Usage

This function is used to safely retrieve user information from both the public.users and auth.users tables.

```javascript
// Example usage in JavaScript
const { data, error } = await supabase.rpc('get_user_info', { user_id: 'some-uuid' });
// Or to get current user's info
const { data, error } = await supabase.rpc('get_user_info');
```

#### Security Considerations

- Uses `SECURITY DEFINER` to run with the privileges of the function creator
- Implements authorization checks to ensure users can only access their own data or admins can access any user's data
- Joins the public.users and auth.users tables to get complete user information
- Returns empty result if the user is not authorized to access the requested data

## Important Notes on Functions

1. **SECURITY DEFINER**: Functions marked with `SECURITY DEFINER` run with the privileges of the user who created the function, not the user who calls it. This is important for security functions that need to access tables that the calling user might not have direct access to.

2. **Avoiding Recursion**: The security functions query the `auth.users` table directly instead of the `public.users` table to avoid potential infinite recursion when RLS policies are applied.

3. **Performance**: These functions are designed to be lightweight and efficient, as they may be called frequently during query execution.

4. **Maintenance**: When modifying these functions, be careful not to introduce security vulnerabilities or performance issues.

## Best Practices for Creating New Functions

1. **Use SECURITY INVOKER by default**: Only use SECURITY DEFINER when absolutely necessary.

2. **Validate inputs**: Always validate function inputs to prevent SQL injection.

3. **Handle errors gracefully**: Include proper error handling in functions.

4. **Document thoroughly**: Include comments explaining what the function does and how it should be used.

5. **Test extensively**: Test functions with different inputs and user roles to ensure they work as expected.
