"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { use, useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";

// Define types
type Student = {
  id: string;
  full_name: string;
  email: string;
  role: string;
  created_at: string;
  last_sign_in_at: string | null; // Updated to use auth.users.last_sign_in_at
  identity_document?: string | null; // Número de documento (e.g., RUT)
};

type Certificate = {
  id: string;
  certificate_number: string;
  issue_date: string;
  expiry_date: string | null;
  status: string;
  course: {
    name: string;
  };
};

export default function StudentDetail({ params }: { params: { id: string } }) {
  const router = useRouter();
  const unwrappedParams = use(params);
  const studentId = unwrappedParams.id;

  // State
  const [student, setStudent] = useState<Student | null>(null);
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleting, setDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Status colors and labels
  const statusColors = {
    ACTIVE: "bg-green-100 text-green-800",
    REVOKED: "bg-red-100 text-red-800",
    EXPIRED: "bg-yellow-100 text-yellow-800",
  };

  const statusLabels = {
    ACTIVE: "Activo",
    REVOKED: "Revocado",
    EXPIRED: "Expirado",
  };

  // Fetch student data
  useEffect(() => {
    async function fetchStudentData() {
      try {
        setLoading(true);
        setError(null);

        // Fetch student using the get_user_info RPC function
        // This function safely accesses both public.users and auth.users
        const { data: userData, error: userError } = await supabase.rpc(
          "get_user_info",
          { user_id: studentId },
        );

        if (userError) throw userError;
        if (!userData || userData.length === 0)
          throw new Error("Alumno no encontrado");

        // Format the student data and include identity_document
        let documento: string | null = null;
        // Intentar obtener identity_document desde public.users
        const { data: docData, error: docError } = await supabase
          .from("users")
          .select("identity_document")
          .eq("id", studentId)
          .single();
        if (!docError && docData) documento = docData.identity_document;
        const studentData = {
          ...userData[0],
          full_name:
            `${userData[0].first_name || ""} ${userData[0].last_name || ""}`.trim(),
          identity_document: documento,
        };

        // Check if the student has the correct role
        if (studentData.role !== "student")
          throw new Error("El usuario no es un alumno");

        setStudent(studentData);

        // Fetch certificates for this student
        const { data: certificatesData, error: certificatesError } =
          await supabase
            .from("certificates")
            .select(`
            id,
            certificate_number,
            issue_date,
            expiry_date,
            status,
            course:course_id (
              title
            )
          `)
            .eq("user_id", studentId)
            .order("issue_date", { ascending: false });

        if (certificatesError) throw certificatesError;
        setCertificates(certificatesData || []);
      } catch (error: unknown) {
        console.error("Error fetching student data:", error);
        setError(
          error instanceof Error
            ? error.message
            : "Error al cargar los datos del alumno",
        );
      } finally {
        setLoading(false);
      }
    }

    fetchStudentData();
  }, [studentId]);

  // Handle student deletion
  const handleDelete = async () => {
    try {
      setDeleting(true);
      setError(null);

      // Delete certificates associated with this student
      const { error: certificatesError } = await supabase
        .from("certificates")
        .delete()
        .eq("user_id", studentId);

      if (certificatesError) throw certificatesError;

      // Delete the student from the users table
      const { error: studentError } = await supabase
        .from("users")
        .delete()
        .eq("id", studentId);

      if (studentError) throw studentError;

      // We should also delete the user from auth.users but that requires admin access
      // This would typically be done in a Supabase Function or trusted backend

      router.push("/panel-admin/alumnos");
    } catch (error: unknown) {
      console.error("Error deleting student:", error);
      setError(
        error instanceof Error ? error.message : "Error al eliminar el alumno",
      );
      setDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-gray-500">Cargando datos del alumno...</p>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-red-50 border-l-4 border-red-500 p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
            <div className="mt-4">
              <Link
                href="/panel-admin/alumnos"
                className="text-sm text-red-700 font-medium hover:text-red-600"
              >
                ← Volver a la lista de alumnos
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // No student found
  if (!student) {
    return (
      <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Alumno no encontrado
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>El alumno solicitado no existe o ha sido eliminado.</p>
            </div>
            <div className="mt-4">
              <Link
                href="/panel-admin/alumnos"
                className="text-sm text-yellow-700 font-medium hover:text-yellow-600"
              >
                ← Volver a la lista de alumnos
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header with actions */}
      <div className="md:flex md:items-center md:justify-between mb-6">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            {student.full_name}
          </h2>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <Link
            href="/panel-admin/alumnos"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            ← Volver
          </Link>
          <Link
            href={`/panel-admin/alumnos/editar/${student.id}`}
            className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Editar
          </Link>
          <button
            onClick={() => setShowDeleteConfirm(true)}
            className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Eliminar
          </button>
        </div>
      </div>

      {/* Student details */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Información del Alumno
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Detalles personales y cuenta.
          </p>
        </div>
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Nombre completo
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {student.full_name}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Correo electrónico
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {student.email}
              </dd>
            </div>
            {/* Documento de Identidad */}
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Documento de Identidad
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {student.identity_document || "-"}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Fecha de registro
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {new Date(student.created_at).toLocaleDateString("es-ES", {
                  day: "2-digit",
                  month: "long",
                  year: "numeric",
                })}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Último acceso
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {student.last_sign_in_at
                  ? new Date(student.last_sign_in_at).toLocaleDateString(
                      "es-ES",
                      {
                        day: "2-digit",
                        month: "long",
                        year: "numeric",
                      },
                    )
                  : "Nunca ha iniciado sesión"}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Total de certificados
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {certificates.length}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      {/* Certificates section */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Certificados
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Listado de certificados emitidos para este alumno.
            </p>
          </div>
          <div>
            <Link
              href={`/panel-admin/certificados/nuevo?studentId=${student.id}`}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Nuevo Certificado
            </Link>
          </div>
        </div>
        <div className="border-t border-gray-200">
          {certificates.length === 0 ? (
            <div className="px-4 py-8 text-center">
              <p className="text-gray-500">
                Este alumno aún no tiene certificados emitidos.
              </p>
              <div className="mt-4">
                <Link
                  href={`/panel-admin/certificados/nuevo?studentId=${student.id}`}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Emitir Certificado
                </Link>
              </div>
            </div>
          ) : (
            <div className="flow-root">
              <ul className="divide-y divide-gray-200">
                {certificates.map((certificate) => (
                  <li key={certificate.id} className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-indigo-600 truncate">
                        {certificate.certificate_number}
                      </p>
                      <div className="ml-2 flex-shrink-0 flex">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            statusColors[
                              certificate.status as keyof typeof statusColors
                            ] || "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {statusLabels[
                            certificate.status as keyof typeof statusLabels
                          ] || certificate.status}
                        </span>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-gray-500">
                          Curso: {certificate.course.title}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <p>
                          Emitido:{" "}
                          {new Date(certificate.issue_date).toLocaleDateString(
                            "es-ES",
                          )}
                        </p>
                      </div>
                    </div>
                    <div className="mt-2 flex justify-end">
                      <Link
                        href={`/panel-admin/certificados/${certificate.id}`}
                        className="text-sm font-medium text-indigo-600 hover:text-indigo-500"
                      >
                        Ver detalles
                      </Link>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>

      {/* Delete confirmation modal */}
      {showDeleteConfirm && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div
              className="fixed inset-0 transition-opacity"
              aria-hidden="true"
              onClick={() => setShowDeleteConfirm(false)}
            >
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span
              className="hidden sm:inline-block sm:align-middle sm:h-screen"
              aria-hidden="true"
            >
              &#8203;
            </span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg
                      className="h-6 w-6 text-red-600"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                      />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3
                      className="text-lg leading-6 font-medium text-gray-900"
                      id="modal-title"
                    >
                      Eliminar Alumno
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        ¿Estás seguro de que deseas eliminar este alumno? Esta
                        acción no se puede deshacer y eliminará permanentemente
                        al alumno y todos sus certificados asociados del
                        sistema.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDelete}
                  disabled={deleting}
                >
                  {deleting ? "Eliminando..." : "Eliminar"}
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setShowDeleteConfirm(false)}
                >
                  Cancelar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
