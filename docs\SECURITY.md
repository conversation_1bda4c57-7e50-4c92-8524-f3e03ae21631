# 🛡️ Security Best Practices

> **See Also:** [INDEX.md](./INDEX.md) | [PLANNING.md](./PLANNING.md) | [RULES.md](./RULES.md) | [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md)

This document outlines security best practices for the QR Course Platform Scaffold.

---

## Modernization Roadmap Reference

- See the [README](../README.md#-plan-de-modernización-y-mejora-continua-faseshitos) for the full phased plan.
- Security is a transversal concern, referenced in all phases of the roadmap.
- All security practices are cross-referenced in [PLANNING.md](./PLANNING.md), [INDEX.md](./INDEX.md), and [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md).

---

## 1. Row Level Security (RLS)
- **Enable RLS** on all Supabase tables
- Write explicit policies for each role (admin, student, instructor)
- Test policies with different user roles
- Document all policies in `docs/` or as SQL migrations

---

## 2. Secrets & Environment Variables
- Store all secrets in `.env.local` (never commit to git)
- Use GitHub Actions secrets for CI/CD
- Rotate keys regularly

---

## 3. HTTPS & Secure Cookies
- Always use HTTPS in production
- Set `secure`, `httpOnly`, and `SameSite` attributes on cookies
- Use Supabase Auth with secure session storage

---

## 4. Audit Logging
- Enable logging for all critical actions (login, certificate issue, data changes)
- Use Supabase triggers or Edge Functions for audit trails
- Store logs in a separate, immutable table if needed

---

## 5. User Data & Privacy
- Only request and store necessary user data
- Hash sensitive data where possible
- Comply with GDPR/CCPA if applicable

---

## 6. Dependency Management
- Keep all dependencies up-to-date (see CI/CD automation)
- Monitor for vulnerabilities (Dependabot, npm audit)

---

## 7. Access Control
- Use role-based access in both frontend and backend
- Never trust client-side role checks alone
- Validate all API requests on the server

---

## 8. References
- [Supabase Security Docs](https://supabase.com/docs/guides/auth/row-level-security)
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)

---