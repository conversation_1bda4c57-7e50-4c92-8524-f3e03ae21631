/**
 * @fileoverview Modern Button Component (2025 UI/UX Trends)
 *
 * Componente de botón moderno que implementa las tendencias UI/UX 2025:
 * - Efectos de glassmorphism
 * - Micro-interacciones avanzadas
 * - Animaciones fluidas
 * - Estados de carga modernos
 * - Efectos de brillo y gradientes
 */

"use client";

import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { Loader2 } from "lucide-react";
import * as React from "react";
import { cn } from "@/lib/utils";

const modernButtonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden group",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg hover:shadow-xl hover:-translate-y-0.5",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-lg hover:shadow-xl hover:-translate-y-0.5",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-md hover:shadow-lg hover:-translate-y-0.5",
        ghost: "hover:bg-accent hover:text-accent-foreground hover:scale-105",
        link: "text-primary underline-offset-4 hover:underline hover:text-primary/80",
        glass:
          "glass text-foreground hover:bg-glass-background/80 border border-glass-border backdrop-blur-glass hover:scale-105",
        neuro: "neuro text-foreground hover:neuro-inset active:neuro-inset",
        gradient:
          "bg-gradient-to-r from-primary to-secondary text-primary-foreground shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-primary/90 hover:to-secondary/90",
        glow: "bg-primary text-primary-foreground shadow-lg hover:shadow-[0_0_30px_rgba(59,130,246,0.4)] hover:-translate-y-0.5",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-lg px-3",
        lg: "h-11 rounded-xl px-8",
        xl: "h-12 rounded-2xl px-10 text-base",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

export interface ModernButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof modernButtonVariants> {
  asChild?: boolean;
  loading?: boolean;
  loadingText?: string;
  icon?: React.ComponentType<{ className?: string }>;
  iconPosition?: "left" | "right";
  ripple?: boolean;
}

const ModernButton = React.forwardRef<HTMLButtonElement, ModernButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      loading = false,
      loadingText,
      icon: Icon,
      iconPosition = "left",
      ripple = true,
      children,
      onClick,
      ...props
    },
    ref,
  ) => {
    const [ripples, setRipples] = React.useState<
      Array<{ id: number; x: number; y: number }>
    >([]);
    const rippleId = React.useRef(0);

    const Comp = asChild ? Slot : "button";

    const handleClick = React.useCallback(
      (event: React.MouseEvent<HTMLButtonElement>) => {
        if (ripple && !loading) {
          const rect = event.currentTarget.getBoundingClientRect();
          const x = event.clientX - rect.left;
          const y = event.clientY - rect.top;

          const newRipple = { id: rippleId.current++, x, y };
          setRipples((prev) => [...prev, newRipple]);

          // Remover el ripple después de la animación
          setTimeout(() => {
            setRipples((prev) => prev.filter((r) => r.id !== newRipple.id));
          }, 600);
        }

        if (onClick && !loading) {
          onClick(event);
        }
      },
      [onClick, loading, ripple],
    );

    // Cuando se usa asChild, simplificamos el componente para evitar conflictos con React.Children.only
    if (asChild) {
      return (
        <Comp
          className={cn(modernButtonVariants({ variant, size, className }))}
          ref={ref}
          onClick={handleClick}
          disabled={loading || props.disabled}
          {...props}
        >
          {children}
        </Comp>
      );
    }

    return (
      <Comp
        className={cn(modernButtonVariants({ variant, size, className }))}
        ref={ref}
        onClick={handleClick}
        disabled={loading || props.disabled}
        {...props}
      >
        {/* Efecto de brillo en hover */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-in-out" />

        {/* Ripple effects */}
        {ripples.map((ripple) => (
          <span
            key={ripple.id}
            className="absolute bg-white/30 rounded-full animate-ping"
            style={{
              left: ripple.x - 10,
              top: ripple.y - 10,
              width: 20,
              height: 20,
            }}
          />
        ))}

        {/* Contenido del botón */}
        <div className="relative z-10 flex items-center justify-center gap-2">
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              {loadingText && <span>{loadingText}</span>}
            </>
          ) : (
            <>
              {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
              {children}
              {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
            </>
          )}
        </div>
      </Comp>
    );
  },
);
ModernButton.displayName = "ModernButton";

/**
 * Botón flotante de acción (FAB) moderno
 */
interface ModernFABProps extends Omit<ModernButtonProps, "size" | "variant"> {
  size?: "sm" | "md" | "lg";
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
}

const fabPositions = {
  "bottom-right": "fixed bottom-6 right-6",
  "bottom-left": "fixed bottom-6 left-6",
  "top-right": "fixed top-20 right-6",
  "top-left": "fixed top-20 left-6",
};

const fabSizes = {
  sm: "h-12 w-12",
  md: "h-14 w-14",
  lg: "h-16 w-16",
};

export const ModernFAB = React.forwardRef<HTMLButtonElement, ModernFABProps>(
  (
    { className, size = "md", position = "bottom-right", children, ...props },
    ref,
  ) => {
    return (
      <ModernButton
        ref={ref}
        variant="glow"
        className={cn(
          "rounded-full shadow-2xl hover:shadow-[0_0_40px_rgba(59,130,246,0.5)] z-50",
          fabPositions[position],
          fabSizes[size],
          className,
        )}
        {...props}
      >
        {children}
      </ModernButton>
    );
  },
);
ModernFAB.displayName = "ModernFAB";

/**
 * Grupo de botones modernos
 */
interface ModernButtonGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  orientation?: "horizontal" | "vertical";
  variant?: "default" | "glass" | "outline";
}

export const ModernButtonGroup = React.forwardRef<
  HTMLDivElement,
  ModernButtonGroupProps
>(
  (
    {
      className,
      children,
      orientation = "horizontal",
      variant = "default",
      ...props
    },
    ref,
  ) => {
    return (
      <div
        ref={ref}
        className={cn(
          "inline-flex",
          orientation === "horizontal" ? "flex-row" : "flex-col",
          variant === "glass" && "glass rounded-xl p-1",
          variant === "outline" && "border border-border rounded-xl p-1",
          className,
        )}
        role="group"
        {...props}
      >
        {React.Children.map(children, (child, index) => {
          if (React.isValidElement(child)) {
            return React.cloneElement(child, {
              className: cn(
                child.props.className,
                orientation === "horizontal"
                  ? index === 0
                    ? "rounded-r-none"
                    : index === React.Children.count(children) - 1
                      ? "rounded-l-none"
                      : "rounded-none"
                  : index === 0
                    ? "rounded-b-none"
                    : index === React.Children.count(children) - 1
                      ? "rounded-t-none"
                      : "rounded-none",
              ),
            });
          }
          return child;
        })}
      </div>
    );
  },
);
ModernButtonGroup.displayName = "ModernButtonGroup";

export { ModernButton, modernButtonVariants };
