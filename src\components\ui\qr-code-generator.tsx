"use client";

import QRCode from "qrcode";
import { useEffect, useState } from "react";

interface QRCodeGeneratorProps {
  url: string;
  size?: number;
  errorCorrectionLevel?: "L" | "M" | "Q" | "H";
  margin?: number;
  className?: string;
  onGenerated?: (dataUrl: string) => void;
  onError?: (error: Error) => void;
}

export function QRCodeGenerator({
  url,
  size = 300,
  errorCorrectionLevel = "H",
  margin = 1,
  className = "",
  onGenerated,
  onError,
}: QRCodeGeneratorProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const generateQR = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Verificar que QRCode esté disponible
        if (typeof QRCode === "undefined" || !QRCode.toDataURL) {
          throw new Error(
            "QRCode library not available or method toDataURL not found",
          );
        }

        // Generar el código QR
        const qrCodeData = await QRCode.toDataURL(url, {
          errorCorrectionLevel,
          margin,
          width: size,
        });

        setQrCodeUrl(qrCodeData);
        if (onGenerated) {
          onGenerated(qrCodeData);
        }
      } catch (err) {
        console.error("Error generating QR Curse:", err);
        const error =
          err instanceof Error
            ? err
            : new Error("Unknown error generating QR Curse");
        setError(error);
        if (onError) {
          onError(error);
        }
        // Establecer una imagen de placeholder en caso de error
        setQrCodeUrl(
          "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=",
        );
      } finally {
        setIsLoading(false);
      }
    };

    generateQR();
  }, [url, size, errorCorrectionLevel, margin, onGenerated, onError]);

  if (isLoading) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 ${className}`}
        style={{ width: size, height: size }}
      >
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className={`flex flex-col items-center justify-center bg-gray-100 ${className}`}
        style={{ width: size, height: size }}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-10 w-10 text-red-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
          />
        </svg>
        <p className="text-xs text-red-500 mt-2 text-center px-2">
          Error al generar QR
        </p>
      </div>
    );
  }

  return (
    <img
      src={qrCodeUrl || ""}
      alt="QR Curse"
      className={className}
      style={{ width: size, height: size }}
      onError={(e) => {
        console.error("Error loading QR Curse image");
        e.currentTarget.src =
          "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
      }}
    />
  );
}

export default QRCodeGenerator;
